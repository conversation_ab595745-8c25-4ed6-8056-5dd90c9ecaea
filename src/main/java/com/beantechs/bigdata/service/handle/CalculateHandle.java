package com.beantechs.bigdata.service.handle;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import com.beantechs.bigdata.service.config.RedisConfig;
import com.beantechs.bigdata.service.config.ScenarioConstants;
import com.beantechs.bigdata.service.entity.*;
import com.beantechs.bigdata.service.entity.req.QueryWinScenarioReq;
import com.beantechs.bigdata.service.entity.req.QueryWinScenarioTreeReq;
import com.beantechs.bigdata.service.entity.req.SceneTagServiceRecommendReq;
import com.beantechs.bigdata.service.entity.resp.*;
import com.beantechs.bigdata.service.mapper.BusinessPrimaryJdbcTemplateService;
import com.beantechs.bigdata.service.mapper.DataserviceSlaveJdbcTemplateService;
import com.beantechs.service.ResBody.ResponseEntity;
import com.beantechs.service.exception.DIYException;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CalculateHandle {

    private static final BigDecimal negativeOne = new BigDecimal("-1");
    private static final BigDecimal one = new BigDecimal("2");
    private static final String FREE_TIME_REDIS_KEY = "DATASERVICE:SCENE:API:MODEL:CLEARMIND";

    @Value("${normal.weight}")
    private BigDecimal normalWeight;

    @Value("${redis.prefix.scenario.statistics}")
    private String redisPreStatistics;

    private final AsyncExecuteThreadPoolHandle asyncExecuteThreadPoolHandle;

    private final BusinessPrimaryJdbcTemplateService businessPrimaryJdbcTemplateService;

    private final DataserviceSlaveJdbcTemplateService dataserviceSlaveJdbcTemplateService;

    private final JedisPool jedisPool;

    @Value("${redis.database}")
    private Integer indexDb;

    @Value("${redis.key.pk.scores.prefix}")
    private String pkScorePrefixKey;

    @Value("${redis.key.feedback.value.prefix}")
    private String feedbackValuePrefixKey;

    @Value("${redis.key.scenario.avg.pvalue.prefix}")
    private String scenarioAvgPValuePrefixKey;

    public CalculateHandle(
            AsyncExecuteThreadPoolHandle asyncExecuteThreadPoolHandle,
            @Qualifier("jedisPool") JedisPool jedisPool,
            BusinessPrimaryJdbcTemplateService businessPrimaryJdbcTemplateService,
            DataserviceSlaveJdbcTemplateService dataserviceSlaveJdbcTemplateService
    ) {
        this.asyncExecuteThreadPoolHandle = asyncExecuteThreadPoolHandle;
        this.jedisPool = jedisPool;
        this.businessPrimaryJdbcTemplateService = businessPrimaryJdbcTemplateService;
        this.dataserviceSlaveJdbcTemplateService = dataserviceSlaveJdbcTemplateService;
    }


    /**
     * @throws DIYException e
     */
    public Set<ScenarioCreditVo> calScenarioVictoryCodes(QueryWinScenarioReq request) throws DIYException {
        StopWatch stopWatch = new StopWatch();

        stopWatch.start("query latest cal scenario scores(查询最后一次剧本计算信用值)...");
        ScenarioPkBo bo = getScenarioBo(request);
        List<Scenario> scenarioList = bo.getScenarioList();
        stopWatch.stop();

        stopWatch.start("add feedback p value(添加feedback的p值)");
        //计算feedback值相加扰动
        addFeedbackValue(bo);
        stopWatch.stop();

        stopWatch.start("statistics scenario group by trigger type and scenario code(根据剧本Code和触发器进行pk信用值原始数据分类)...");
        List<Scenario> triggerTypeVictory = new ArrayList<>();
        //根据触发器分类
        Map<String, List<Scenario>> triggerTypeMap = scenarioList.stream().collect(Collectors.groupingBy(Scenario::getTriggerType));
        Set<String> triggerTypes = triggerTypeMap.keySet();
        //根据剧本id分类
        Map<String, List<Scenario>> scenarioCodeMap = scenarioList.stream().collect(Collectors.groupingBy(Scenario::getScenarioCode));
        Set<String> codeKeyList = scenarioCodeMap.keySet();
        Map<String, List<Scenario>> triggerTypeMapFailed = new HashMap<>();

        //全胜剧本
        HashSet<String> scenarioCodeVictoryDestList = new HashSet<>();
        stopWatch.stop();

        //pk统计结果
        stopWatch.start("first time pk(第一轮pk,取触发器分类的结果进行pk,保存信用值高的)...");
        //获胜的剧本id
        List<Scenario> scenarioCodeVictoryList = calVictoryAndFailedScenario(triggerTypeMap);
        stopWatch.stop();

        stopWatch.start("second time pk(第二轮pk,取剧本Code分类的结果进行pk,保存全胜剧本)...");
        for (String code : codeKeyList) {
            //剧本对应的触发器数量
            List<Scenario> list = scenarioCodeMap.get(code);
            if (Objects.nonNull(list)) {
                int size = list.size();
                long count = scenarioCodeVictoryList.stream().filter(o -> o.getScenarioCode().equals(code)).count();
                //如果获胜的剧本id数量==触发器数量，即全胜，该剧本获胜
                if (size == count) {
                    scenarioCodeVictoryDestList.add(code);
                }
            }
        }
        stopWatch.stop();

        stopWatch.start("save failed by pk(保存pk失败的用于第三轮回溯)...");
        triggerTypes.forEach(v -> {
            List<Scenario> typeList = triggerTypeMap.get(v);
            Optional<Scenario> first = typeList.stream().filter(t -> scenarioCodeVictoryDestList.contains(t.getScenarioCode())).findFirst();
            //如果有值保存胜利对象
            if (first.isPresent()) {
                triggerTypeVictory.add(first.get());
            } else {
                List<Scenario> collect = typeList.stream().filter(t -> 1 == t.getTriggerNum()).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)) {
                    triggerTypeMapFailed.put(v, collect);
                }
            }
        });
        stopWatch.stop();

        stopWatch.start("third time pk(第三轮pk,取触发器分类的结果进行pk,保存信用值高的)...");
        List<ScenarioCreditVo> collect = getScenarioCreditVoList(triggerTypeVictory, triggerTypeMapFailed);
        stopWatch.stop();

        stopWatch.start("开线程,异步保存调用次数....");
        if (1 == request.getActionSign() || 2 == request.getActionSign()) {
            ScenarioCreditVo scenarioCreditVo = collect.stream().max(Comparator.comparing(ScenarioCreditVo::getScenarioDestCredit)).orElse(null);
            List<ScenarioCreditVo> resultList = new ArrayList<>();
            resultList.add(scenarioCreditVo);
            asyncExecuteThreadPoolHandle.execute(bo, resultList, "1");
            return new HashSet<>(resultList);
        }
        asyncExecuteThreadPoolHandle.execute(bo, collect, "1");
        stopWatch.stop();

        if (log.isDebugEnabled()) {
            log.warn(stopWatch.prettyPrint());
        }
        return new HashSet<>(collect);
    }


    /**
     * 计算剧本主体逻辑——版本2
     */
    public WinScenario calScenarioVictoryCodesV2(QueryWinScenarioReq request, String version) throws DIYException {
        WinScenario result = new WinScenario(request.getBeanId(), request.getVin());

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("query latest cal scenario scores(查询最后一次剧本计算信用值)...");
        ScenarioPkBo bo = getScenarioBo(request);
        final List<Scenario> scenarioList = bo.getScenarioList();
        stopWatch.stop();

        stopWatch.start("add feedback p value(添加feedback的p值)");
        //计算feedback值相加扰动
        addFeedbackValue(bo);
        result.setAllScenarioCredit(scenarioList.stream().map(ScenarioCreditVo::of).collect(Collectors.toList()));
        stopWatch.stop();

        stopWatch.start("statistics scenario group by trigger type and scenario code(根据剧本Code和触发器进行pk信用值原始数据分类)...");
        bo.initGroupMap();
        stopWatch.stop();

        // pk统计结果
        stopWatch.start("first time pk(第一轮pk,取触发器分类的结果进行pk,保存信用值高的)...");
        // 获胜的剧本code
        List<Scenario> winScenarioList = calVictoryAndFailedScenario(bo.getTriggerTypeMap());
        stopWatch.stop();

        stopWatch.start("second time pk(第二轮pk,取剧本Code分类的结果进行pk,保存全胜剧本)...");
        // 全胜剧本
        Set<String> allWinScenarioCode = getAllWinScenarioCode(bo, winScenarioList);
        stopWatch.stop();

        stopWatch.start("save failed by pk(保存pk失败的用于第三轮回溯)...");
        ScenarioPkResultBo pkResultBo = savePKFailUseByThirdPK(bo, allWinScenarioCode);
        stopWatch.stop();

        stopWatch.start("third time pk(第三轮pk,取触发器分类的结果进行pk,保存信用值高的)...");
        final List<ScenarioCreditVo> creditVoList = getScenarioCreditVoList(pkResultBo.getWinList(), pkResultBo.getFailedMap());
        if (log.isDebugEnabled()) {
            log.debug("creditVoList -> {}", JSON.toJSONString(creditVoList));
        }
        stopWatch.stop();

        stopWatch.start("开线程,异步保存调用次数....");
        saveCallApiRecords(request.getActionSign(), creditVoList, result, bo, version);
        stopWatch.stop();

        if (log.isDebugEnabled()) {
            log.warn(stopWatch.prettyPrint());
        }
        return result;
    }


    /**
     * 计算剧本主体逻辑——版本2
     */
    public WinScenario calScenarioVictoryCodesV4(QueryWinScenarioTreeReq request) throws DIYException {
        WinScenario result = new WinScenario(request.getBeanId(), request.getVin());

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("query latest cal scenario scores(查询最后一次剧本计算信用值)...");
        ScenarioPkBo bo = getScenarioBoV4(request);
        final List<Scenario> scenarioList = bo.getScenarioList();
        stopWatch.stop();

        if (CollectionUtils.isEmpty(scenarioList)) {
            asyncExecuteThreadPoolHandle.execute(bo, new ArrayList<>(), "4");
            return result;
        }

        stopWatch.start("add feedback p value(添加feedback的p值)");
        //计算feedback值相加扰动
        addFeedbackValue(bo);
        result.setAllScenarioCredit(scenarioList.stream().map(ScenarioCreditVo::of).collect(Collectors.toList()));
        stopWatch.stop();

        stopWatch.start("statistics scenario group by trigger type and scenario code(根据剧本Code和触发器进行pk信用值原始数据分类)...");
        bo.initGroupMap();
        stopWatch.stop();

        // pk统计结果
        stopWatch.start("first time pk(第一轮pk,取触发器分类的结果进行pk,保存信用值高的)...");
        // 获胜的剧本code
        List<Scenario> winScenarioList = calVictoryAndFailedScenario(bo.getTriggerTypeMap());
        stopWatch.stop();

        stopWatch.start("second time pk(第二轮pk,取剧本Code分类的结果进行pk,保存全胜剧本)...");
        // 全胜剧本
        Set<String> allWinScenarioCode = getAllWinScenarioCode(bo, winScenarioList);
        stopWatch.stop();

        stopWatch.start("save failed by pk(保存pk失败的用于第三轮回溯)...");
        ScenarioPkResultBo pkResultBo = savePKFailUseByThirdPK(bo, allWinScenarioCode);
        stopWatch.stop();

        stopWatch.start("third time pk(第三轮pk,取触发器分类的结果进行pk,保存信用值高的)...");
        final List<ScenarioCreditVo> creditVoList = getScenarioCreditVoListV4(pkResultBo.getWinList(), pkResultBo.getFailedMap());
        if (log.isDebugEnabled()) {
            log.debug("creditVoList -> {}", JSON.toJSONString(creditVoList));
        }
        stopWatch.stop();

        stopWatch.start("开线程,异步保存调用次数....");
        saveTreeCallApiRecords(request.getActionSign(), creditVoList, result, bo);
        stopWatch.stop();

        if (log.isDebugEnabled()) {
            log.warn(stopWatch.prettyPrint());
        }
        return result;
    }

    private void saveCallApiRecords(Integer request, List<ScenarioCreditVo> creditVoList, WinScenario result, ScenarioPkBo bo, String apiVersion) {
        List<ScenarioCreditVo> collect = creditVoList.stream().collect(Collectors.groupingBy(ScenarioCreditVo::getScenarioCode))
                .values().stream().flatMap(set -> set.size() > 1 ? set.subList(0, 1).stream() : set.stream())
                .collect(Collectors.toList());
        if (1 == request || 2 == request) {
            final List<ScenarioCreditVo> resultList = Collections.singletonList(collect.stream().max(Comparator.comparing(ScenarioCreditVo::getScenarioDestCredit)).orElse(null));
            result.setVictoryScenarioCredit(resultList);
            asyncExecuteThreadPoolHandle.execute(bo, resultList, apiVersion);
        } else {
            result.setVictoryScenarioCredit(collect);
            asyncExecuteThreadPoolHandle.execute(bo, collect, apiVersion);
        }
    }

    private void saveTreeCallApiRecords(Integer request, List<ScenarioCreditVo> creditVoList, WinScenario result, ScenarioPkBo bo) {
        List<ScenarioCreditVo> collect = creditVoList.stream().collect(Collectors.groupingBy(ScenarioCreditVo::getScenarioCode))
                .values().stream().flatMap(set -> set.size() > 1 ? set.subList(0, 1).stream() : set.stream())
                .collect(Collectors.toList());
        if (1 == request || 2 == request) {
            final List<ScenarioCreditVo> resultList = Collections.singletonList(collect.stream().max(Comparator.comparing(ScenarioCreditVo::getScenarioDestCredit)).map(scenarioCreditVo -> {
                scenarioCreditVo.setTreeCode(scenarioCreditVo.getScenarioCode());
                scenarioCreditVo.setScenarioCode(null);
                scenarioCreditVo.setScenarioName(null);
                return scenarioCreditVo;
            }).orElse(null));
            result.setVictoryScenarioCredit(resultList);
            asyncExecuteThreadPoolHandle.execute(bo, resultList, "4");
        } else {
            List<ScenarioCreditVo> list = new ArrayList<>();
            for (ScenarioCreditVo scenarioCreditVo : collect) {
                scenarioCreditVo.setTreeCode(scenarioCreditVo.getScenarioCode());
                scenarioCreditVo.setScenarioCode(null);
                scenarioCreditVo.setScenarioName(null);
                list.add(scenarioCreditVo);
            }
            result.setVictoryScenarioCredit(list);
            asyncExecuteThreadPoolHandle.execute(bo, collect, "4");
        }
    }


    /**
     * 查询剧本
     *
     * @param winScenario    获胜剧本
     * @param queryScoreList 需要查询剧本的列表
     * @return 响应体
     */
    public WinScenario calScenarioVictoryCodesStepThree(WinScenario winScenario, List<String> queryScoreList) {
        final List<KvResp> kvQuery = dataserviceSlaveJdbcTemplateService.queryScenarioPkValuesInfoByCodes(queryScoreList);
        winScenario.setQueryScoreResultList(queryScoreList.stream()
                .map(obj -> new KvResp(obj, kvQuery.stream().filter(query -> query.getKey().equals(obj)).findFirst().orElse(new KvResp(obj, null)).getValue()))
                .filter(obj -> null != obj.getValue())
                .distinct()
                .collect(Collectors.toList()));
        return winScenario;
    }

    /**
     * 查询剧本
     *
     * @param winScenario    获胜剧本
     * @param queryScoreList 需要查询剧本的列表
     * @return 响应体
     */
    public WinScenario calScenarioVictoryCodesStepFour(WinScenario winScenario, List<String> queryScoreList, String vin) {
        if (CollectionUtils.isEmpty(queryScoreList)) {
            return winScenario;
        }
        final List<KvResp> kvQuery = dataserviceSlaveJdbcTemplateService.queryPkTreeInfoByVinTree(vin, queryScoreList)
                .stream()
                .map(obj -> new KvResp(obj.getTreeCode(), obj.getScenarioDestCredit().doubleValue()))
                .collect(Collectors.toList());
        winScenario.setQueryScoreResultList(kvQuery);
        return winScenario;
    }


    private List<Scenario> getScenarioList(List<String> values) {
        if (CollectionUtils.isEmpty(values)) {
            return Collections.emptyList();
        }

        List<Scenario> scenarioList = new ArrayList<>(values.size());

        values.forEach(v -> {
            List<Scenario> list = JSON.parseArray(v, Scenario.class);
            scenarioList.addAll(list);
        });

        if (log.isDebugEnabled()) {
            log.warn("cal pk scenario trigger type list:{}", JSON.toJSONString(scenarioList));
        }
        return scenarioList;
    }

    /**
     * 提取剧本信息
     *
     * @param request 请求消息体
     * @return 剧本列表信息
     * @throws DIYException 自定义异常
     */
    @NotNull
    private ScenarioPkBo getScenarioBo(QueryWinScenarioReq request) throws DIYException {
        final String vin = request.getVin();
        final String beanId = request.getBeanId();
        List<String> codes = request.getCodes().stream().distinct().collect(Collectors.toList());
        if (log.isDebugEnabled()) {
            log.warn("cal pk params code list:{}", JSON.toJSONString(codes));
        }

        //redis读取
        int size = codes.size();
        List<String> pkKeyList = new ArrayList<>(size);
        List<String> oldKeyList = new ArrayList<>(size);
        List<String> currentKeyList = new ArrayList<>(size);
        listValueSet(vin, beanId, codes, pkKeyList, oldKeyList, currentKeyList);

        //获取缓存
        Jedis jedis = RedisConfig.getResource(jedisPool, indexDb);
        List<String> pkValues = jedis.mget(pkKeyList.toArray(new String[0]));
        List<String> oldValues = jedis.mget(oldKeyList.toArray(new String[0]));
        List<String> currentValues = jedis.mget(currentKeyList.toArray(new String[0]));
        jedis.close();

        if (Objects.nonNull(pkValues)) {
            pkValues = pkValues.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(pkValues)) {
                throw new DIYException("can not find scenario_group_by_trigger_type_list collect!");
            }
        }

        ScenarioPkBo bo = new ScenarioPkBo(vin, beanId, request.getActionSign());
        bo.setScenarioList(getScenarioList(pkValues));
        bo.setCodeList(codes);
        bo.initOldFeedbackValueMap(codes, oldValues);
        bo.initCurrentFeedbackValueMap(codes, currentValues);
        return bo;
    }

    /**
     * 提取剧本信息
     *
     * @param request 请求消息体
     * @return 剧本列表信息
     * @throws DIYException 自定义异常
     */
    @NotNull
    private ScenarioPkBo getScenarioBoV4(QueryWinScenarioTreeReq request) throws DIYException {
        final String vin = request.getVin();
        final String beanId = request.getBeanId();
        List<String> codes = request.getTreeCodes().stream().distinct().collect(Collectors.toList());
        if (log.isDebugEnabled()) {
            log.warn("cal pk params code list:{}", JSON.toJSONString(codes));
        }

        //redis读取
        int size = codes.size();
        List<String> oldKeyList = new ArrayList<>(size);
        List<String> currentKeyList = new ArrayList<>(size);
        listValueSetV4(vin, beanId, codes, oldKeyList, currentKeyList);

        Jedis jedis = RedisConfig.getResource(jedisPool, indexDb);
        List<String> oldValues = jedis.mget(oldKeyList.toArray(new String[0]));
        List<String> currentValues = jedis.mget(currentKeyList.toArray(new String[0]));
        jedis.close();

        ScenarioPkBo bo = new ScenarioPkBo(vin, beanId, request.getActionSign());
        //将scenarioCode替换为treeCode计算
        List<Scenario> list = new ArrayList<>();
        for (Scenario item : dataserviceSlaveJdbcTemplateService.queryPkTreeTriggerInfoByVinTree(vin, codes)) {
            item.setScenarioCode(item.getTreeCode());
            item.setScenarioName(item.getTreeName());
            list.add(item);
        }
        bo.setScenarioList(list);
        bo.setCodeList(codes);
        bo.initOldFeedbackValueMap(codes, oldValues);
        bo.initCurrentFeedbackValueMap(codes, currentValues);
        return bo;
    }

    private void listValueSetV5(String vin, String beanId, List<String> codes, List<String> currentKeyList) {
        final String today = LocalDate.now().toString();
        codes.forEach(code -> currentKeyList.add(feedbackValuePrefixKey + vin + ScenarioConstants.JAVA_COMMON_MAO_STR + beanId + ScenarioConstants.JAVA_COMMON_MAO_STR + code + ScenarioConstants.JAVA_COMMON_MAO_STR + today));
    }


    public ResponseEntity<List<ScenarioTagRecommendResp>> sceneTagServiceRecommendHandle(SceneTagServiceRecommendReq req) {
        List<SceneTagServiceRecommendTreeTag> treeTagList = req.getTreeTagList();
        List<String> tagList = req.getTagList();

        //从redis获取剧本列表信息
        Jedis jedis = RedisConfig.getResource(jedisPool, indexDb);
        List<String> redisCollect = jedis.mget(treeTagList.stream().map(item -> pkScorePrefixKey + item.getScenarioCode()).toArray(String[]::new));

        //过滤非空对象，获取可用剧本
        List<String> notEmptyRedisCollect = new ArrayList<>();
        if (!CollectionUtils.isEmpty(redisCollect)) {
            notEmptyRedisCollect = redisCollect.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        List<SceneTagServiceRecommendTreeTag> validCollect = new ArrayList<>();
        if (!CollectionUtils.isEmpty(notEmptyRedisCollect)) {
            tagList = setTriggerParam(notEmptyRedisCollect, treeTagList, validCollect, tagList);
        } else {
            log.warn("redis中未获取到剧本信用分，redis key:{}, 入参剧本列表:{}", pkScorePrefixKey, treeTagList.stream().map(SceneTagServiceRecommendTreeTag::getScenarioCode).collect(Collectors.toList()));
            List<ScenarioTagRecommendResp> collect = new ArrayList<>(treeTagList.stream().map(t -> {
                ScenarioTagRecommendResp scenarioTagRecommendResp = new ScenarioTagRecommendResp();
                scenarioTagRecommendResp.setScenarioCode(t.getScenarioCode());
                scenarioTagRecommendResp.setTreeCode(t.getTreeCode());
                return scenarioTagRecommendResp;
            }).toList());
            Collections.shuffle(collect);
            return ResponseEntity.responseBySucceedData(collect);
        }

        //设置标签数量pk参数
        setLabelNumPKParam(tagList, validCollect);

        //有效的剧本code列表
        List<String> validScenarioCodeList = validCollect.stream().map(SceneTagServiceRecommendTreeTag::getScenarioCode).collect(Collectors.toList());

        List<String> currentKeyList = new ArrayList<>(validScenarioCodeList.size());
        //根据剧本codes取对应用户反馈分
        listValueSetV5(req.getVin(), req.getBeanId(), validScenarioCodeList, currentKeyList);

        List<String> currentValues = jedis.mget(currentKeyList.toArray(new String[0]));

        //实时反馈重排
        realResortCore(req.getVin(), req.getBeanId(), tagList, validCollect);
        //实时剧本等级重排
        realReSortLevelValue(validCollect, validScenarioCodeList, jedis);

        BigDecimal weight = normalWeight;
        for (int i = 0; i < validScenarioCodeList.size(); i++) {
            String scenarioCode = validScenarioCodeList.get(i);
            int finalI = i;
            validCollect.forEach(obj -> {
                if (obj.getScenarioCode().equals(scenarioCode)) {
                    calFeedbackP(currentValues, finalI, obj, weight);
                }
            });
        }

        //已执行过滤列表
        Set<String> executedScenario = jedis.smembers(
                "SMART_AUDIO:EXECUTED:TREECODE:" +
                req.getVin() +
                ":" +
                req.getBeanId() +
                ":" +
                LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        jedis.close();

        executedScenario.add(req.getExecutedTreeCode());

        List<SceneTagServiceRecommendTreeTag> list = new ArrayList<>();

        List<ScenarioTagRecommendResp> collect = validCollect.stream()
                .sorted(Comparator.comparing(SceneTagServiceRecommendTreeTag::getScenarioLevel).thenComparing(Comparator.comparing(SceneTagServiceRecommendTreeTag::getGaussianScore).reversed()))
                .map(item -> {
                    if (executedScenario.contains(item.getScenarioCode())) {
                        list.add(item);
                    } else {
                        return item;
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .map(CalculateHandle::convertResp)
                .distinct()
                .collect(Collectors.toList());
        collect.addAll(list.stream().map(CalculateHandle::convertResp).toList());
        asyncExecuteThreadPoolHandle.saveExecutedScenario2Redis(req.getVin(), req.getBeanId(), req.getExecutedTreeCode());
        return ResponseEntity.responseBySucceedData(collect.stream().distinct().collect(Collectors.toList()));
    }

    private static void setLabelNumPKParam(List<String> tagList, List<SceneTagServiceRecommendTreeTag> validCollect) {
        for (SceneTagServiceRecommendTreeTag item : validCollect) {
            ArrayList<String> strings = new ArrayList<>(tagList);
            strings.retainAll(item.getTagList());
            item.setTagsMatchList(strings);
            item.setTagsMatchNum(strings.size());
            item.setTagsSumNum(tagList.size());
        }
    }

    @NotNull
    private static List<String> setTriggerParam(
            List<String> notEmptyRedisCollect,
            List<SceneTagServiceRecommendTreeTag> treeTagList,
            List<SceneTagServiceRecommendTreeTag> validCollect,
            List<String> tagList
    ) {
        //转实体类
        List<List<Scenario>> redisListEntity = notEmptyRedisCollect.stream().map(item -> JSON.parseArray(item, Scenario.class)).toList();
        List<String> triggerList = new ArrayList<>();
        List<String> finalTagList = tagList;
        redisListEntity.forEach(obj -> treeTagList.forEach(item -> {
            if (item.getScenarioCode().equals(obj.get(0).getScenarioCode())) {
                //取剧本与计算的P值
                item.setAdvanceComputeScore(obj.get(0).getScenarioTotalCredit());
                if (CollectionUtils.isEmpty(item.getTagList())) {
                    //如果是触发器剧本而不是标签，重设tagList
                    List<String> triggerTypeList = obj.stream().map(Scenario::getTriggerType).toList();
                    if (!CollectionUtils.isEmpty(triggerTypeList)) {
                        item.setTagList(triggerTypeList);
                    } else {
                        item.setTagList(finalTagList);
                    }
                }
                //将可触发的触发器添加到tagList
                Optional<String> first = obj.stream().filter(s -> s.getScenarioCode().equals(item.getScenarioCode())).map(Scenario::getTriggerType).findFirst();
                first.ifPresent(triggerList::add);
                validCollect.add(item);
            }
        }));
        //合并触发器
        tagList.addAll(triggerList);
        //去重
        tagList = tagList.stream().distinct().collect(Collectors.toList());
        return tagList;
    }


    public ResponseEntity<JSONObject> sceneTagServiceRecommendHandleTest(SceneTagServiceRecommendReq req) {
        JSONObject json = new JSONObject();
        List<SceneTagServiceRecommendTreeTag> treeTagList = req.getTreeTagList();
        List<String> tagList = req.getTagList();

        //从redis获取剧本列表信息
        Jedis jedis = RedisConfig.getResource(jedisPool, indexDb);
        List<String> redisCollect = jedis.mget(treeTagList.stream().map(item -> pkScorePrefixKey + item.getScenarioCode()).toArray(String[]::new));
        json.put("步骤一：redis批量获取的pk信息, redis前缀：" + pkScorePrefixKey, redisCollect);
        //过滤非空对象，获取可用剧本
        List<String> notEmptyRedisCollect = redisCollect.stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<SceneTagServiceRecommendTreeTag> validCollect = new ArrayList<>();
        if (!CollectionUtils.isEmpty(notEmptyRedisCollect)) {
            //转实体类
            tagList = setTriggerParam(notEmptyRedisCollect, treeTagList, validCollect, tagList);
        } else {
            log.warn("redis中未获取到剧本信用分，redis key:{}, 入参剧本列表:{}", pkScorePrefixKey, treeTagList.stream().map(SceneTagServiceRecommendTreeTag::getScenarioCode).collect(Collectors.toList()));
            json.put("redis中未获取到剧本信用分, 实际返回结果:", new ArrayList<>());
            return ResponseEntity.responseBySucceedData(json);
        }

        json.put("步骤二：有效的PK结果设置预计算信用分结果和触发器转换到tagList", JSON.toJSONString(validCollect));
        //设置核心pk参数
        setLabelNumPKParam(tagList, validCollect);
        json.put("步骤三：根据标签数量设置PK信息", JSON.toJSONString(validCollect));

        //有效的剧本code列表
        List<String> validScenarioCodeList = validCollect.stream().map(SceneTagServiceRecommendTreeTag::getScenarioCode).collect(Collectors.toList());
        List<String> currentKeyList = new ArrayList<>(validScenarioCodeList.size());
        //根据剧本codes取对应用户反馈分
        listValueSetV5(req.getVin(), req.getBeanId(), validScenarioCodeList, currentKeyList);
        List<String> currentValues = jedis.mget(currentKeyList.toArray(new String[0]));
        json.put("步骤五：获取实时用户反馈分, redis key:" + currentKeyList + ", 反馈分结果", currentValues);

        //实时反馈重排
        realResortCore(req.getVin(), req.getBeanId(), tagList, validCollect);
        json.put("步骤六：实时反馈分重排结果", JSON.toJSONString(validCollect));
        //实时剧本等级重排
        realReSortLevelValue(validCollect, validScenarioCodeList, jedis);
        json.put("步骤七：剧本等级重排结果", JSON.toJSONString(validCollect));

        BigDecimal weight = normalWeight;
        for (int i = 0; i < validScenarioCodeList.size(); i++) {
            String scenarioCode = validScenarioCodeList.get(i);
            int finalI = i;
            validCollect.forEach(obj -> {
                if (obj.getScenarioCode().equals(scenarioCode)) {
                    calFeedbackP(currentValues, finalI, obj, weight);
                }
            });
        }

        json.put("步骤八：（实时反馈分重排 + 剧本等级重排）公式计算分数 + 预计算信用分 + 实时用户反馈分, 高斯扰动结果", JSON.toJSONString(validCollect));
        //已执行过滤列表
        Set<String> executedScenario = jedis.smembers(
                "SMART_AUDIO:EXECUTED:TREECODE:" +
                req.getVin() +
                ":" +
                req.getBeanId() +
                ":" +
                LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        jedis.close();

        executedScenario.add(req.getExecutedTreeCode());
        json.put("步骤九：已执行的剧本", JSON.toJSONString(executedScenario));

        List<SceneTagServiceRecommendTreeTag> list = new ArrayList<>();

        List<ScenarioTagRecommendResp> collect = validCollect.stream()
                .sorted(Comparator.comparing(SceneTagServiceRecommendTreeTag::getScenarioLevel).thenComparing(Comparator.comparing(SceneTagServiceRecommendTreeTag::getGaussianScore).reversed()))
                .map(item -> {
                    if (executedScenario.contains(item.getScenarioCode())) {
                        list.add(item);
                    } else {
                        return item;
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .map(CalculateHandle::convertResp)
                .distinct()
                .collect(Collectors.toList());
        json.put("步骤十：根据剧本等级重排再根据最终排名重排", JSON.toJSONString(collect));
        collect.addAll(list.stream().map(CalculateHandle::convertResp).toList());
        json.put("步骤十一：将已执行剧本拉至末尾", JSON.toJSONString(collect));
        asyncExecuteThreadPoolHandle.saveExecutedScenario2Redis(req.getVin(), req.getBeanId(), req.getExecutedTreeCode());
        List<ScenarioTagRecommendResp> result = collect.stream().distinct().collect(Collectors.toList());
        json.put("步骤十二：最终返回结果", JSON.toJSONString(result));
        return ResponseEntity.responseBySucceedData(json);
    }


    @NotNull
    private static ScenarioTagRecommendResp convertResp(SceneTagServiceRecommendTreeTag item) {
        ScenarioTagRecommendResp resp = new ScenarioTagRecommendResp();
        BeanUtils.copyProperties(item, resp);
        return resp;
    }


    private void realReSortLevelValue(List<SceneTagServiceRecommendTreeTag> treeTagList, List<String> scenarioCodes, Jedis jedis) {
        String[] array = scenarioCodes.toArray(new String[0]);
        List<String> arrayValues = jedis.hmget("SMART_AUDIO:SCENARIO:LEVEL:INFO", array);

        for (SceneTagServiceRecommendTreeTag tree : treeTagList) {
            Integer index = null;
            for (int i = 0; i < scenarioCodes.size(); i++) {
                if (scenarioCodes.get(i).equals(tree.getScenarioCode())) {
                    index = i;
                    break;
                }
            }

            String treeLevel;
            if (index == null) {
                treeLevel = "300";
            } else {
                treeLevel = arrayValues.get(index);
            }
            tree.setScenarioLevel(Integer.parseInt(null == treeLevel ? "300" : treeLevel));

            // 使用 BigDecimal 进行计算
            BigDecimal one = new BigDecimal("1.00");
            BigDecimal threeHundred = new BigDecimal("300");
            BigDecimal pointZeroTwo = new BigDecimal("0.02");

            // 计算 exponent = 0.02 * (treeLevel - 300)
            BigDecimal treeLevelBD = new BigDecimal(StringUtils.isBlank(treeLevel) ? "300" : treeLevel);
            BigDecimal exponent = pointZeroTwo.multiply(treeLevelBD.subtract(threeHundred));

            // 计算 expValue = Math.pow(Math.E, exponent.doubleValue())
            BigDecimal expValue = BigDecimal.valueOf(Math.exp(exponent.doubleValue()));

            // 计算 denominator = expValue + 1
            BigDecimal denominator = expValue.add(one);

            // 计算 divide = 1.00 / denominator
            BigDecimal divide = one.divide(denominator, 3, RoundingMode.HALF_UP);

            // 更新 tree 的值
            tree.setTreeRealSortCredit(tree.getTreeRealSortCredit().add(divide));
            tree.setTreeLevelScore(divide);
        }
    }


    private static void calFeedbackP(List<String> currentValues, int i, SceneTagServiceRecommendTreeTag sceneTagServiceRecommendTreeTag, BigDecimal weight) {
        BigDecimal feedbackValue = new BigDecimal("0.00");

        if (!Objects.isNull(sceneTagServiceRecommendTreeTag)) {
            feedbackValue = feedbackValue.add(sceneTagServiceRecommendTreeTag.getTreeRealSortCredit());
        }

        if (!CollectionUtils.isEmpty(currentValues)) {
            String currentP = currentValues.get(i);
            if (StringUtils.isNotBlank(currentP)) {
                feedbackValue = feedbackValue.add(TypeUtils.castToBigDecimal(currentP));
            }
        }

        if (feedbackValue.compareTo(negativeOne) < 0) {
            feedbackValue = negativeOne;
        }
        if (feedbackValue.compareTo(one) > 0) {
            feedbackValue = one;
        }

        BigDecimal scenarioTotalCredit = sceneTagServiceRecommendTreeTag.getAdvanceComputeScore();
        BigDecimal totalCredit = feedbackValue.add(scenarioTotalCredit);
        sceneTagServiceRecommendTreeTag.setTotalCredit(totalCredit);

        BigDecimal finalTotal = (new BigDecimal("3.8").multiply(totalCredit.subtract(new BigDecimal("0.2")))).divide(new BigDecimal("4.8"), 3, RoundingMode.HALF_UP).add(new BigDecimal("0.2"));
        sceneTagServiceRecommendTreeTag.setTotalCreditReset(finalTotal);

        BigDecimal gaussian = weight.multiply(BigDecimal.valueOf(new Random().nextGaussian())).add(finalTotal).setScale(3, RoundingMode.HALF_UP);
        sceneTagServiceRecommendTreeTag.setGaussianScore(gaussian);
    }


    private void realResortCore(String vin, String beanId, List<String> tagList, List<SceneTagServiceRecommendTreeTag> treeTagList) {
        HashMap<String, Integer> tagMatchTimesMap = new HashMap<>();
        tagList.forEach(tag -> treeTagList.forEach(treeTag -> mergeTagMatchTime(tag, treeTag, tagMatchTimesMap)));

        //计算剧本信用值
        treeTagList.forEach(tree -> {
            AtomicReference<BigDecimal> scenarioLabelScore = new AtomicReference<>(BigDecimal.ZERO);
            tree.getTagList().forEach(tag -> {
                if (tagMatchTimesMap.containsKey(tag)) {
                    BigDecimal matchNum = BigDecimal.valueOf(tree.getTagsMatchNum());
                    BigDecimal logValue = BigDecimal.valueOf(Math.log((double) (tagList.size() + 1) / (tagMatchTimesMap.get(tag) + 1)));
                    BigDecimal increment = BigDecimal.ONE.divide(matchNum, 10, RoundingMode.HALF_UP).multiply(logValue).setScale(3, RoundingMode.HALF_UP);
                    scenarioLabelScore.updateAndGet(v -> v.add(increment));
                }
            });

            BigDecimal roundedScenarioLabelScore = scenarioLabelScore.get().setScale(3, RoundingMode.HALF_UP);
            tree.setScenarioLabelScore(roundedScenarioLabelScore);

            BigDecimal tagsMatchNum = BigDecimal.valueOf(tree.getTagsMatchNum());
            BigDecimal labelWeight = BigDecimal.ONE.add(BigDecimal.ONE.divide(BigDecimal.ONE.add(BigDecimal.valueOf(Math.exp(-0.8 * (tagsMatchNum.doubleValue() - 6)))), 10, RoundingMode.HALF_UP)).setScale(3, RoundingMode.HALF_UP);
            tree.setLabelWeight(labelWeight);

            BigDecimal pFeedbackLabel = labelWeight.multiply(roundedScenarioLabelScore).setScale(3, RoundingMode.HALF_UP);
            tree.setPFeedbackLabel(pFeedbackLabel);
        });

        //根据标签权重分数排序
        List<String> treeCodes = new ArrayList<>();
        List<SceneTagServiceRecommendTreeTag> onLineSortList = treeTagList.stream().sorted(Comparator.comparing(SceneTagServiceRecommendTreeTag::getPFeedbackLabel).reversed()).toList();
        onLineSortList.forEach(item -> treeCodes.add(item.getTreeCode()));

        int tagIndex = 1;
        List<ResortBo> sortOnLineList = new ArrayList<>();
        for (int i = 0; i < onLineSortList.size(); i++) {
            SceneTagServiceRecommendTreeTag tag = onLineSortList.get(i);
            int rank = tagIndex;

            if (i > 0 && tag.getPFeedbackLabel().equals(onLineSortList.get(i - 1).getPFeedbackLabel())) {
                rank = sortOnLineList.get(i - 1).getRank();
            }
            sortOnLineList.add(new ResortBo(tag.getTreeCode(), 1, rank));
            tagIndex++;
        }


        //获取离线重排结果
        //获取前一天的日期并转为字符串
        LocalDate yesterday = LocalDate.now().minusDays(1);
        String yesterdayAsString = yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        AiDwdScenarioServiceCombineRecommendationToMysqlDf combin = dataserviceSlaveJdbcTemplateService.getOne(vin, beanId, yesterdayAsString);
        List<ResortBo> sortOffLineList;
        //如果不为空, 重排离线
        if (!Objects.isNull(combin)) {
            sortOffLineList = Arrays.stream(combin.getTreeCodeRanks().split(","))
                    .map(item -> {
                        String[] split = item.split(":");
                        return new ResortBo(split[0], Integer.parseInt(split[1]), Integer.parseInt(split[2]));
                    }).filter(item -> treeCodes.contains(item.getTreeCode())).toList();
        } else {
            sortOffLineList = new ArrayList<>();
        }

        //合并重排结果
        List<SceneTagServiceRecommendTreeTag> invalidList = treeTagList.stream().filter(item -> item.getPFeedbackLabel().compareTo(new BigDecimal("0.00")) == 0).toList();

        List<ResortBo> resultBo = sortOnLineList.stream().peek(item -> {
            Optional<ResortBo> first = sortOffLineList.stream().filter(bo -> item.getTreeCode().equals(bo.getTreeCode())).findFirst();
            if (first.isPresent()) {
                ResortBo resortBo = first.get();
                item.setNum(resortBo.getNum() + item.getNum());
            }
            if (!CollectionUtils.isEmpty(invalidList) && invalidList.stream().map(SceneTagServiceRecommendTreeTag::getTreeCode).toList().contains(item.getTreeCode())) {
                item.setRank(999);
            } else {
                if (first.isPresent()) {
                    ResortBo resortBo = first.get();
                    item.setRank(resortBo.getRank() + item.getRank());
                }
            }
        }).sorted(Comparator.comparing(ResortBo::getNum).reversed().thenComparing(ResortBo::getRank)).toList();


        //最终排序
        for (int i = 1; i <= resultBo.size(); i++) {
            ResortBo resortBo = resultBo.get(i - 1);

            // 使用 BigDecimal 进行计算
            BigDecimal index = BigDecimal.valueOf(resortBo.getRank());
            BigDecimal twenty = new BigDecimal("20.00");
            BigDecimal pointTwo = new BigDecimal("0.2");
            BigDecimal one = new BigDecimal("1.00");
            BigDecimal pointFive = new BigDecimal("0.50");
            BigDecimal two = new BigDecimal("2.00");

            // 计算 exponent = 0.2 * (i - 20)
            BigDecimal exponent = pointTwo.multiply(index.subtract(twenty));

            // 计算 expValue = Math.pow(Math.E, exponent.doubleValue())
            BigDecimal expValue = BigDecimal.valueOf(Math.exp(exponent.doubleValue()));

            // 计算 denominator = expValue + 1
            BigDecimal denominator = expValue.add(one);

            // 计算 feedback = 2.00 / denominator - 0.50
            BigDecimal feedback = two.divide(denominator, 3, RoundingMode.HALF_UP).subtract(pointFive).setScale(3, RoundingMode.HALF_UP);

            String treeCode = resortBo.getTreeCode();
            treeTagList.forEach(tree -> {
                if (resortBo.getRank() == 0 && resortBo.getNum() == 0) {
                    tree.setTreeRealSortCredit(BigDecimal.ZERO.setScale(3, RoundingMode.HALF_UP));
                } else {
                    if (tree.getTreeCode().equals(treeCode)) {
                        tree.setTreeRealSortCredit(feedback);
                    }
                }
            });
        }

    }


    private static void mergeTagMatchTime(String tag, SceneTagServiceRecommendTreeTag treeTag, HashMap<String, Integer> tagMatchTimesMap) {
        if (treeTag.getTagList().contains(tag)) {
            tagMatchTimesMap.merge(tag, 1, Integer::sum);
        }
    }


    /**
     * 赋值
     */
    private void listValueSet(String vin, String beanId, List<String> codes, List<String> pkKeyList, List<String> oldKeyList, List<String> currentKeyList) {
        final String today = LocalDate.now().toString();
        codes.forEach(code -> {
            pkKeyList.add(pkScorePrefixKey + code);
            oldKeyList.add(feedbackValuePrefixKey + vin + ScenarioConstants.JAVA_COMMON_MAO_STR + beanId + ScenarioConstants.JAVA_COMMON_MAO_STR + code + ScenarioConstants.JAVA_COMMON_MAO_STR + ScenarioConstants.COMMON_SERVICE_STRING_OLD);
            currentKeyList.add(feedbackValuePrefixKey + vin + ScenarioConstants.JAVA_COMMON_MAO_STR + beanId + ScenarioConstants.JAVA_COMMON_MAO_STR + code + ScenarioConstants.JAVA_COMMON_MAO_STR + today);
        });
    }

    /**
     * 赋值
     */
    private void listValueSetV4(String vin, String beanId, List<String> codes, List<String> oldKeyList, List<String> currentKeyList) {
        final String today = LocalDate.now().toString();
        codes.forEach(code -> {
            oldKeyList.add(feedbackValuePrefixKey + vin + ScenarioConstants.JAVA_COMMON_MAO_STR + beanId + ScenarioConstants.JAVA_COMMON_MAO_STR + code + ScenarioConstants.JAVA_COMMON_MAO_STR + ScenarioConstants.COMMON_SERVICE_STRING_OLD);
            currentKeyList.add(feedbackValuePrefixKey + vin + ScenarioConstants.JAVA_COMMON_MAO_STR + beanId + ScenarioConstants.JAVA_COMMON_MAO_STR + code + ScenarioConstants.JAVA_COMMON_MAO_STR + today);
        });
    }


    /**
     * 添加反馈值
     *
     * @param bo 剧本信息
     */
    private void addFeedbackValue(ScenarioPkBo bo) {
        Map<String, ScenarioCreditSimpleBo> creditSimpleBoMap = Maps.newHashMapWithExpectedSize(bo.getScenarioList().size());

        bo.getScenarioList().forEach(item -> {
            ScenarioCreditSimpleBo simpleBo = creditSimpleBoMap.get(item.getScenarioCode());
            if (Objects.isNull(simpleBo)) {
                calFeedbackValue(bo, creditSimpleBoMap, item);
            } else {
                item.setScenarioTotalCredit(simpleBo.getTotal());
                item.setScenarioDestCredit(simpleBo.getDest());
            }
        });

        // 使用完毕
        bo.setOldFeedbackValueMap(null);
        bo.setCurrentFeedbackValueMap(null);
    }

    /**
     * 添加反馈分
     *
     * @param bo                剧本PK列表
     * @param creditSimpleBoMap 剧本分数对象
     * @param item              剧本信息
     */
    private void calFeedbackValue(ScenarioPkBo bo, Map<String, ScenarioCreditSimpleBo> creditSimpleBoMap, Scenario item) {
        BigDecimal feedbackValue = new BigDecimal("0.00");

        String currentValue = bo.getCurrentFeedbackValueMap().get(item.getScenarioCode());
        if (!StringUtils.isBlank(currentValue)) {
            feedbackValue = feedbackValue.add(TypeUtils.castToBigDecimal(currentValue));
        }
        currentValue = bo.getOldFeedbackValueMap().get(item.getScenarioCode());
        if (StringUtils.isNotBlank(currentValue)) {
            feedbackValue = feedbackValue.add(new BigDecimal(currentValue));
        }

        if (feedbackValue.compareTo(negativeOne) < 0) {
            feedbackValue = negativeOne;
        }
        if (feedbackValue.compareTo(one) > 0) {
            feedbackValue = one;
        }

        item.setFeedbackCredit(feedbackValue);
        final BigDecimal againTotalCredit = feedbackValue.add(item.getScenarioTotalCredit());
        item.setScenarioTotalCredit(againTotalCredit);
        item.setScenarioDestCredit(normalWeight.multiply(BigDecimal.valueOf(new Random().nextGaussian())).add(againTotalCredit).setScale(3, RoundingMode.HALF_UP));
        creditSimpleBoMap.put(item.getScenarioCode(), ScenarioCreditSimpleBo.create(item.getScenarioCode(), item.getScenarioTotalCredit(), item.getScenarioDestCredit()));
    }


    public ScenarioStatisticsNumResp queryScenarioStatisticsNum(String scenarioCode) throws DIYException {
        final ScenarioInfo scenarioInfo = getScenarioInfo(scenarioCode);
        ScenarioStatisticsNumResp scenarioStatisticsNumResps = setStatisticsNumValues(scenarioInfo);

        //获取当天的pvuv
        List<ScenarioStatisticsNumSbroadcast> pvList = new ArrayList<>();
        List<ScenarioStatisticsNumSbroadcast> uvList = new ArrayList<>();

        Jedis jedis = RedisConfig.getResource(jedisPool, indexDb);
        String codeKeyPre = toDayPVUVHandle(scenarioInfo, pvList, uvList, jedis);

        scenarioStatisticsNumResps.setPv(pvList);
        scenarioStatisticsNumResps.setUv(uvList);

        //取最近的剧本信用值
        totalAndDestCreditValuesSet(scenarioCode, scenarioStatisticsNumResps, scenarioInfo.getScenarioCode(), jedis);

        //计算total
        String total = jedis.hget(codeKeyPre, "total");
        jedis.close();
        scenarioStatisticsNumResps.setTotalPv(StringUtils.isBlank(total) ? 0 : JSON.parseObject(total).getInteger("pv"));
        return scenarioStatisticsNumResps;
    }

    private void totalAndDestCreditValuesSet(String scenarioCode, ScenarioStatisticsNumResp scenarioStatisticsNumResps, String scenarioCodeDb, Jedis jedis) {
        final Scenario scenario = dataserviceSlaveJdbcTemplateService.queryScenarioMaxDateScoreByCode(scenarioCodeDb);
        if (null != scenario) {
            if (scenario.getScenarioTotalWithAvgCredit().equals(scenario.getScenarioTotalCredit())) {
                String scenarioAvgValueStr = jedis.get(scenarioAvgPValuePrefixKey + scenarioCode + ScenarioConstants.JAVA_COMMON_MAO_STR + LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                //如果没有反馈值
                if (StringUtils.isBlank(scenarioAvgValueStr)) {
                    scenarioStatisticsNumResps.setScenarioTotalCredit(Optional.ofNullable(scenario.getScenarioTotalCredit()).orElse(new BigDecimal("0.00")));
                    scenarioStatisticsNumResps.setScenarioDestCredit(Optional.ofNullable(scenario.getScenarioDestCredit()).orElse(new BigDecimal("0.00")));
                } else {
                    //如果有反馈值
                    BigDecimal scenarioAvgValue = BigDecimal.valueOf(Double.parseDouble(scenarioAvgValueStr)).setScale(3, RoundingMode.HALF_UP).add(Optional.ofNullable(scenario.getScenarioTotalCredit()).orElse(new BigDecimal("0.00")));
                    BigDecimal disturbance = normalWeight.multiply(BigDecimal.valueOf(new Random().nextGaussian())).add(scenarioAvgValue).setScale(3, RoundingMode.HALF_UP);
                    scenarioStatisticsNumResps.setScenarioTotalCredit(scenarioAvgValue);
                    scenarioStatisticsNumResps.setScenarioDestCredit(disturbance);
                }
            } else {
                scenarioStatisticsNumResps.setScenarioTotalCredit(Optional.of(scenario.getScenarioTotalWithAvgCredit()).orElse(Optional.ofNullable(scenario.getScenarioTotalCredit()).orElse(new BigDecimal("0.00"))));
                scenarioStatisticsNumResps.setScenarioDestCredit(Optional.ofNullable(scenario.getScenarioDestWithAvgCredit()).orElse(Optional.ofNullable(scenario.getScenarioDestCredit()).orElse(new BigDecimal("0.00"))));
            }
        } else {
            scenarioStatisticsNumResps.setScenarioTotalCredit(new BigDecimal("0.00"));
            scenarioStatisticsNumResps.setScenarioDestCredit(new BigDecimal("0.00"));
        }
    }

    @NotNull
    private String toDayPVUVHandle(ScenarioInfo scenarioInfo, List<ScenarioStatisticsNumSbroadcast> pvList, List<ScenarioStatisticsNumSbroadcast> uvList, Jedis jedis) {
        final String codeKeyPre = redisPreStatistics + ScenarioConstants.JAVA_COMMON_MAO_STR + scenarioInfo.getScenarioCode();
        Set<String> hkeys = jedis.hkeys(codeKeyPre);
        hkeys.remove("total");
        final ArrayList<String> keyList = new ArrayList<>(hkeys);
        final List<String> map = jedis.hmget(codeKeyPre, keyList.toArray(new String[hkeys.size()]));
        for (int i = 0; i < keyList.size(); i++) {
            final String dt = keyList.get(i);
            final JSONObject jsonObject = JSON.parseObject(map.get(i));
            ScenarioStatisticsNumSbroadcast pv = new ScenarioStatisticsNumSbroadcast();
            ScenarioStatisticsNumSbroadcast uv = new ScenarioStatisticsNumSbroadcast();
            pv.setDate(dt);
            pv.setData(jsonObject.getInteger("pv"));
            uv.setDate(dt);
            uv.setData(jsonObject.getInteger("uv"));
            pvList.add(pv);
            uvList.add(uv);
        }
        //按日期升序排序
        pvList.sort(Comparator.comparing(ScenarioStatisticsNumSbroadcast::getDate));
        uvList.sort(Comparator.comparing(ScenarioStatisticsNumSbroadcast::getDate));
        return codeKeyPre;
    }

    private ScenarioStatisticsNumResp setStatisticsNumValues(ScenarioInfo scenarioInfo) {
        ScenarioStatisticsNumResp scenarioStatisticsNumResps = new ScenarioStatisticsNumResp();
        scenarioStatisticsNumResps.setScenarioId(scenarioInfo.getScenarioId());
        scenarioStatisticsNumResps.setScenarioCode(scenarioInfo.getScenarioCode());
        callNumSuccessNumValueSet(scenarioInfo, scenarioStatisticsNumResps);
        return scenarioStatisticsNumResps;
    }

    private void callNumSuccessNumValueSet(ScenarioInfo scenarioInfo, ScenarioStatisticsNumResp scenarioStatisticsNumResps) {
        Date date = new Date();
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        if (null != scenarioInfo.getValidityStartDate() && scenarioInfo.getValidityStartDate().compareTo(date) > 0) {
            scenarioStatisticsNumResps.setCallNum(0);
            scenarioStatisticsNumResps.setSuccessNum(0);
        } else {
            List<ScenarioStatisticsNumResp> scenarioStatisticsNumRespList = dataserviceSlaveJdbcTemplateService.queryScenarioCallNumByDate(dateStr, scenarioInfo.getScenarioCode());
            if (CollectionUtils.isEmpty(scenarioStatisticsNumRespList)) {
                scenarioStatisticsNumResps.setCallNum(0);
                scenarioStatisticsNumResps.setSuccessNum(0);
                scenarioStatisticsNumResps.setCallNumActionPkCloud(0);
                scenarioStatisticsNumResps.setSuccessNumActionPkCloud(0);
                scenarioStatisticsNumResps.setCallNumActionPkCar(0);
                scenarioStatisticsNumResps.setSuccessNumActionPkCar(0);
            }
            scenarioStatisticsNumRespList.forEach(scenarioStatisticsNumResp -> {
                if (1 == scenarioStatisticsNumResp.getActionSign()) {
                    scenarioStatisticsNumResps.setCallNumActionPkCloud(scenarioStatisticsNumResp.getCallNum());
                    scenarioStatisticsNumResps.setSuccessNumActionPkCloud(scenarioStatisticsNumResp.getSuccessNum());
                }
                if (2 == scenarioStatisticsNumResp.getActionSign()) {
                    scenarioStatisticsNumResps.setCallNumActionPkCar(scenarioStatisticsNumResp.getCallNum());
                    scenarioStatisticsNumResps.setSuccessNumActionPkCar(scenarioStatisticsNumResp.getSuccessNum());
                }
                if (0 == scenarioStatisticsNumResp.getActionSign()) {
                    scenarioStatisticsNumResps.setCallNum(scenarioStatisticsNumResp.getCallNum());
                    scenarioStatisticsNumResps.setSuccessNum(scenarioStatisticsNumResp.getSuccessNum());
                }
            });
        }
    }

    @NotNull
    private ScenarioInfo getScenarioInfo(String scenarioCode) throws DIYException {
        ScenarioInfo scenarioInfo = new ScenarioInfo();
        BeanUtils.copyProperties(Optional.ofNullable(
                businessPrimaryJdbcTemplateService.queryScenarioInfosByCode(scenarioCode)).orElseThrow(() -> new DIYException("can not find scenario info by id is: " + scenarioCode)
        ), scenarioInfo);
        return scenarioInfo;
    }

    /**
     * 第三轮pk,取触发器分类的结果进行pk,保存信用值高的
     *
     * @param winList 获胜剧本列表
     * @param failMap 失败剧本列表
     * @return 根据触发器结果PK获胜的
     */
    private static List<ScenarioCreditVo> getScenarioCreditVoList(List<Scenario> winList, Map<String, List<Scenario>> failMap) {
        winList.addAll(calVictoryAndFailedScenario(failMap));
        return CollectionUtils.isEmpty(winList) ? Collections.emptyList() : winList.stream().map(ScenarioCreditVo::from).collect(Collectors.toList());
    }

    private static List<ScenarioCreditVo> getScenarioCreditVoListV4(List<Scenario> winList, Map<String, List<Scenario>> failMap) {
        winList.addAll(calVictoryAndFailedScenarioV4(failMap));
        return CollectionUtils.isEmpty(winList) ? Collections.emptyList() : winList.stream().map(ScenarioCreditVo::from).collect(Collectors.toList());
    }


    /**
     * 计算成功和失败剧本——第一轮
     *
     * @param triggerTypeMap 触发器分类剧本
     * @return 剧本列表
     */
    private static List<Scenario> calVictoryAndFailedScenario(Map<String, List<Scenario>> triggerTypeMap) {
        List<Scenario> list = new ArrayList<>();
        triggerTypeMap.values().forEach(scenarioList -> list.add(1 == scenarioList.size() ? scenarioList.get(0) : calculatePKFirst(scenarioList)));
        return list;
    }

    private static List<Scenario> calVictoryAndFailedScenarioV4(Map<String, List<Scenario>> triggerTypeMap) {
        List<Scenario> list = new ArrayList<>();
        triggerTypeMap.values().forEach(scenarioList -> list.add(1 == scenarioList.size() ? scenarioList.get(0) : calculatePKFirst(scenarioList)));
        return list.stream().collect(Collectors.groupingBy(Scenario::getTreeCode))
                .values().stream().flatMap(set -> set.size() > 1 ? set.subList(0, 1).stream() : set.stream())
                .collect(Collectors.toList());
    }


    private static Scenario calculatePKFirst(List<Scenario> scenarioList) {
        Scenario scenario;
        //计算最大的抖动信用值
        final List<Scenario> maxDestList = scenarioList.stream().filter(o -> o.getScenarioDestCredit().equals(scenarioList.stream().map(Scenario::getScenarioDestCredit).max(BigDecimal::compareTo).orElse(new BigDecimal("0.00")))).toList();

        // 如果最大信用值有多条则比较抖动前信用值，否则取最大信用值
        if (maxDestList.size() > 1) {
            final List<Scenario> maxTotalList = maxDestList.stream().filter(o -> o.getScenarioTotalCredit().equals(maxDestList.stream().map(Scenario::getScenarioTotalCredit).max(BigDecimal::compareTo).get())).toList();

            // 如果抖动前最大信用值也有多条则取触发器少的剧本，否则取最大抖动前信用值
            scenario = maxTotalList.size() > 1 ? maxTotalList.stream().filter(o -> o.getTriggerNum().equals(maxTotalList.stream().map(Scenario::getTriggerNum).min(Integer::compareTo).get())).toList().get(0) : maxTotalList.get(0);
        } else {
            scenario = maxDestList.get(0);
        }
        return scenario;
    }

    /**
     * 获取第二轮PK之后的全胜剧本
     *
     * @param bo              PK剧本信息
     * @param winScenarioList 获胜剧本
     * @return 全胜剧本
     */
    private static Set<String> getAllWinScenarioCode(ScenarioPkBo bo, List<Scenario> winScenarioList) {
        Set<String> allWinCodeSet = Sets.newHashSetWithExpectedSize(bo.getCodeList().size());
        for (String code : bo.getCodeList()) {
            // 剧本对应的触发器数量
            final List<Scenario> list = bo.getCodeMap().get(code);
            if (Objects.nonNull(list)) {
                final long count = winScenarioList.stream().filter(o -> o.getScenarioCode().equals(code)).count();
                //如果获胜的剧本id数量==触发器数量，即全胜，该剧本获胜
                if (list.size() == count) {
                    allWinCodeSet.add(code);
                }
            }
        }
        return allWinCodeSet;
    }


    /**
     * 保存pk失败的剧本用于第三轮回溯PK
     *
     * @param bo                 PK剧本信息
     * @param allWinScenarioCode 所有已获胜的剧本
     * @return 第三轮PK对象
     */
    private static ScenarioPkResultBo savePKFailUseByThirdPK(ScenarioPkBo bo, Set<String> allWinScenarioCode) {
        List<Scenario> winList = new ArrayList<>();
        Map<String, List<Scenario>> failedMap = new HashMap<>();

        bo.getTriggerTypeMap().forEach((key, scenarioList) -> {
            Optional<Scenario> first = scenarioList.stream().filter(t -> allWinScenarioCode.contains(t.getScenarioCode())).findFirst();
            // 如果有值保存胜利对象
            if (first.isPresent()) {
                winList.add(first.get());
            } else {
                List<Scenario> collect = scenarioList.stream().filter(t -> 1 == t.getTriggerNum()).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect)) {
                    failedMap.put(key, collect);
                }
            }
        });

        ScenarioPkResultBo resultBo = new ScenarioPkResultBo();
        resultBo.setWinList(winList);
        resultBo.setFailedMap(failedMap);
        return resultBo;
    }


    public List<FreeTimes> recommendFreeTimes(String vin, String branId) {
        String key = FREE_TIME_REDIS_KEY + ":" + vin + ":" + branId;
        Jedis jedis = RedisConfig.getResource(jedisPool, indexDb);
        String members = jedis.get(key);
        jedis.close();
        // 移除字符串开头和结尾的空格
        if (StringUtils.isBlank(members)) {
            log.warn("redis key:{}, 未查询到放空时间段推荐信息!", key);
            return new ArrayList<>();
        }
        members = members.trim();
        // 移除字符串开头和结尾的所有引号
        members = members.replaceAll("^\"+|\"+$", "");
        // 移除多余的转义字符
        members = members.replace("\\\"", "\"").replace("\\\\", "\\");

        if (StringUtils.isNotBlank(members)) {
            return JSON.parseArray(members, FreeTimes.class);
        } else {
            log.warn("redis key:{}, 未查询到放空时间段推荐信息!", key);
            return new ArrayList<>();
        }
    }


}
