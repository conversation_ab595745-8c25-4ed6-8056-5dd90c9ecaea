package com.beantechs.bigdata.service.handle;

import com.alibaba.fastjson.JSON;
import com.beantechs.bigdata.service.config.RedisConfig;
import com.beantechs.bigdata.service.config.ScenarioConstants;
import com.beantechs.bigdata.service.config.ScenarioQueryRecordProducer;
import com.beantechs.bigdata.service.entity.*;
import com.beantechs.bigdata.service.entity.business.ScenarioTagInfo;
import com.beantechs.bigdata.service.entity.resp.ScenarioCreditVo;
import com.beantechs.bigdata.service.mapper.BusinessPrimaryJdbcTemplateService;
import com.beantechs.bigdata.service.mapper.DataservicePrimaryJdbcTemplateService;
import com.beantechs.bigdata.service.mapper.DataserviceSlaveJdbcTemplateService;
import com.beantechs.bigdata.service.utils.RedisUtils;
import com.beantechs.service.exception.DIYException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 */
@Component
@Slf4j
@EnableAsync
public class AsyncExecuteThreadPoolHandle {

    private final ScenarioQueryRecordProducer recordProducer;

    private final BusinessPrimaryJdbcTemplateService businessPrimaryJdbcTemplateService;

    private final DataservicePrimaryJdbcTemplateService dataservicePrimaryJdbcTemplateService;

    private final DataserviceSlaveJdbcTemplateService dataserviceSlaveJdbcTemplateService;


    @Value("${spring.application.name}")
    private String application;


    @Value("${environment}")
    private String environment;

    @Value("${feishu.warn.robot.url}")
    private String warnUrl;


    private final JedisPool jedisPool;

    /**
     * redis库:8
     */
    @Value("${redis.database}")
    private Integer indexDb;

    @Value("${day.credit.weight}")
    private BigDecimal dayCreditWeight;

    @Value("${score.credit.weight}")
    private BigDecimal scoreCreditWeight;

    @Value("${scenario.price.updated.weight.multiply}")
    private BigDecimal scenarioPriceUpdatedWeightMultiply;

    @Value("${scenario.price.updated.weight.add}")
    private BigDecimal scenarioPriceUpdatedWeightAdd;

    @Value("${normal.weight}")
    private BigDecimal normalWeight;

    @Value("${normal.price.weight}")
    private BigDecimal normalPriceWeight;

    @Value("${redis.key.without.trigger.pk.scores.prefix}")
    private String pkScenarioScorePrefixKey;


    @Value("${redis.key.pk.scores.prefix}")
    private String pkScorePrefixKey;

    @Value("${redis.key.lock}")
    private String lock;


    public AsyncExecuteThreadPoolHandle(
            ScenarioQueryRecordProducer recordProducer,
            JedisPool jedisPool,
            BusinessPrimaryJdbcTemplateService businessPrimaryJdbcTemplateService,
            DataservicePrimaryJdbcTemplateService dataservicePrimaryJdbcTemplateService,
            DataserviceSlaveJdbcTemplateService dataserviceSlaveJdbcTemplateService
    ) {
        this.recordProducer = recordProducer;
        this.jedisPool = jedisPool;
        this.businessPrimaryJdbcTemplateService = businessPrimaryJdbcTemplateService;
        this.dataservicePrimaryJdbcTemplateService = dataservicePrimaryJdbcTemplateService;
        this.dataserviceSlaveJdbcTemplateService = dataserviceSlaveJdbcTemplateService;
    }


    /**
     * @param bo 调用参数
     */
    @Async
    public void execute(ScenarioPkBo bo, List<ScenarioCreditVo> collect, String apiVersion) {
        List<String> codes = bo.getCodeList();
        final String vin = bo.getVin();
        final String beanId = bo.getBeanId();
        final Integer actionSign = bo.getActionSign();
        final String codesStr = JSON.toJSONString(codes);

        List<ScenarioQueryRecord> recordList = new ArrayList<>(codes.size());

        codes.forEach(code -> {
            Scenario scenario = bo.getScenarioList().stream().filter(scenarioData -> scenarioData.getScenarioCode().equals(code)).findFirst().orElse(null);

            ScenarioQueryRecord scenarioQueryRecord = new ScenarioQueryRecord();
            scenarioQueryRecord.setScenarioCode(code);
            scenarioQueryRecord.setVin(vin);

            if (Objects.nonNull(scenario)) {
                scenarioQueryRecord.setFeedbackCredit(scenario.getFeedbackCredit().doubleValue());
                scenarioQueryRecord.setScenarioTotalCredit(scenario.getScenarioTotalCredit().doubleValue());
                scenarioQueryRecord.setScenarioDestCredit(scenario.getScenarioDestCredit().doubleValue());
                scenarioQueryRecord.setScenarioName(scenario.getScenarioName());
            }

            scenarioQueryRecord.setBeanId(beanId);
            scenarioQueryRecord.setCodes(codesStr);
            ScenarioCreditVo scenarioCreditVo = collect.stream().filter(scenarioData -> {
                if (null != scenarioData.getScenarioCode()) {
                    return scenarioData.getScenarioCode().equals(code);
                }
                if (null != scenarioData.getTreeCode()) {
                    return scenarioData.getTreeCode().equals(code);
                }
                return false;
            }).findFirst().orElse(null);
            scenarioQueryRecord.setStatus(!Objects.isNull(scenarioCreditVo));
            scenarioQueryRecord.setActionSign(actionSign);
            scenarioQueryRecord.setApiVersion(apiVersion);
            recordList.add(scenarioQueryRecord);
        });

        recordProducer.send(recordList);
        log.info("send  call pk api result records[{}]  to kafka successful!", recordList.size());
    }


    @Async
    public void saveExecutedScenario2Redis(String vin, String beanId, String code) {
        if (StringUtils.isNotBlank(code)) {
            String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            try {
                Jedis jedis = RedisConfig.getResource(jedisPool, indexDb);
                jedis.sadd("SMART_AUDIO:EXECUTED:TREECODE:" + vin + ":" + beanId + ":" + date, code);
                jedis.expire("SMART_AUDIO:EXECUTED:TREECODE:" + vin + ":" + beanId + ":" + date, (long) 86400);
                jedis.close();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                log.error("保存redis已执行过滤信息异常! key:{}, value:{}", "SMART_AUDIO:EXECUTED:TREECODE:" + vin + ":" + beanId + ":" + date, code);
            }
        }
    }

    private boolean sentnx() {
        if (RedisUtils.setnx(lock, "1", indexDb, jedisPool) == 1L) {
            RedisUtils.expire(lock, 10, indexDb, jedisPool);
            log.warn("redis设置锁成功,设置过期时间成功,开始计算和保存...");
        } else if (RedisUtils.setnx(lock, "1", indexDb, jedisPool) == 0L) {
            log.warn("redis设置锁失败,重设过期时间...");
            RedisUtils.expire(lock, 10, indexDb, jedisPool);
            log.warn("redis重设过期时间成功,结束任务");
            return true;
        }
        return false;
    }


    /**
     * 多数据源，加方法锁，同一时刻只有一个线程可以访问该方法
     */
    public Boolean queryAndCalculateScenario() {
        //设置分布式锁
        if (sentnx()) return false;


        try {
            //查询剧本信息，构建剧本对象
            List<ScenarioInfo> scenarioInfos = queryAndBuildScenarioObject();

//        List<ScenarioInfo> scenarioSet = scenarioInfos.stream().filter(item -> StringUtils.isBlank(item.getTreeCode())).collect(Collectors.toList());
            List<ScenarioInfo> treeSet = scenarioInfos.stream().filter(item -> StringUtils.isNotBlank(item.getTreeCode())).collect(Collectors.toList());
            List<ScenarioInfo> scenarioSetNew = new ArrayList<>(scenarioInfos);

            //计算剧本分数，并构建PK基础信用分对象
            List<ScenarioPkInfo> scenarioPkInfoList = calScenarioScores(scenarioSetNew);
            List<ScenarioPkInfo> treePKInfoList = calScenarioScores(treeSet);

            //保存剧本基本信息
            final Date date = new Date();
            if (!CollectionUtils.isEmpty(scenarioPkInfoList)) {
                //通过scenarioPKInfoList对触发器进行拆解
                List<Scenario> scenarioList = getScenarioTriggerInfoList(scenarioSetNew, scenarioPkInfoList);

                //根据scenarioCode过滤去重, 忽略treeCode和treeName
                List<ScenarioPkInfo> pkDistinctByScenarioCode = scenarioPkInfoList.stream().filter(distinctByKey(ScenarioPkInfo::getScenarioCode)).collect(Collectors.toList());

                //根据scenarioCode和triggerType过滤去重, 忽略treeCode和treeName
                List<Scenario> triggerDistinctByScenarioCodeWithTrigger = scenarioList.stream()
                        .filter(distinctByKey(Scenario::getScenarioCode, Scenario::getTriggerType))
                        .peek(AsyncExecuteThreadPoolHandle::resetTriggerType)
                        .collect(Collectors.toList());

                save2MySQL(triggerDistinctByScenarioCodeWithTrigger, pkDistinctByScenarioCode, null, date);

                //移除redis剧本缓存信息，并重新写入
                removeRedisOldKey("scenario");
                redisHandle(triggerDistinctByScenarioCodeWithTrigger, null, date);
            }

            if (!CollectionUtils.isEmpty(treePKInfoList)) {
                //拼装剧本剧情树关系
                List<ScenarioPkInfo> treeCollect = treePKInfoList.stream().filter(item -> StringUtils.isNotBlank(item.getTreeCode())).collect(Collectors.toList());
                List<ScenarioRelationTree> scenarioRelationTreeList = treeCollect.stream().map(ScenarioRelationTree::of).distinct().collect(Collectors.toList());

                save2MySQL(null, null, scenarioRelationTreeList, date);

                //移除redis剧本缓存信息，并重新写入
                removeRedisOldKey("tree");
                redisHandle(null, treeCollect, date);
            }
        } finally {
            //释放锁
            RedisUtils.del(jedisPool, indexDb, lock);
        }

        //释放锁
        RedisUtils.del(jedisPool, indexDb, lock);
        return true;
    }

    private void removeRedisOldKey(String type) {
        switch (type) {
            case "scenario":
                log.info("删除redis剧本对象旧值");
                Set<String> delScenarioKeys = RedisUtils.scan(pkScorePrefixKey + ScenarioConstants.JAVA_COMMON_STAR_STR, indexDb, jedisPool, 1000);
                Long scenarioDelNum = RedisUtils.del(jedisPool, indexDb, delScenarioKeys.toArray(new String[0]));
                log.info("保存剧本触发器对应信用分到redis, 删除的旧key:{}, 删除key数量:{}", JSON.toJSONString(delScenarioKeys), scenarioDelNum);
                break;
            case "tree":
                log.info("删除redis剧情树对象旧值");
                Set<String> delTreeKeys = RedisUtils.scan(pkScenarioScorePrefixKey + ScenarioConstants.JAVA_COMMON_STAR_STR, indexDb, jedisPool, 1000);
                Long treeDelNum = RedisUtils.del(jedisPool, indexDb, delTreeKeys.toArray(new String[0]));
                log.info("保存剧本触发器对应信用分到redis, 删除的旧key:{}, 删除key数量:{}", JSON.toJSONString(delTreeKeys), treeDelNum);
                break;
        }
    }

    private void extractedFunctionScenario(List<String> list, Map<String, List<Scenario>> collect, String code) {
        List<Scenario> listByCode = collect.get(code);
        String key = pkScorePrefixKey + code;
        String value = JSON.toJSONString(listByCode);
        list.add(key);
        list.add(value);
    }

    public static String dateTime2String(Date date) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return dateFormat.format(date);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private void redisHandle(List<Scenario> triggerDistinctByScenarioCodeWithTrigger, List<ScenarioPkInfo> treeCollect, Date date) {
        //************************************************剧本根据触发器分类, 一个触发器一条*******************************************************//
        //根据剧本code分组
        if (!CollectionUtils.isEmpty(triggerDistinctByScenarioCodeWithTrigger)) {
            final Map<String, List<Scenario>> scenarioCollect = triggerDistinctByScenarioCodeWithTrigger.stream().collect(Collectors.groupingBy(Scenario::getScenarioCode));

            //组建剧本为key的PK对象放入缓存待用
            List<String> saveScenarioTriggerList = new ArrayList<>();
            scenarioCollect.keySet().forEach(code -> extractedFunctionScenario(saveScenarioTriggerList, scenarioCollect, code));
            String scenarioSet = RedisUtils.mset(jedisPool, indexDb, saveScenarioTriggerList.toArray(new String[0]));
            if (!"OK".equals(scenarioSet)) {
                log.error("保存剧本信息至redis失败!");
            }
            log.warn("保存剧本信息至redis成功,当前时间:{}", dateTime2String(date));
            log.warn("保存redis设置值: 剧本key：{}", JSON.toJSONString(saveScenarioTriggerList));
        }


        //************************************************剧情树不根据触发器分类, 一个剧情树一条*******************************************************//
        if (!CollectionUtils.isEmpty(treeCollect)) {
            //组建剧情树为key的PK对象放入缓存待用
            List<String> saveScenarioList = new ArrayList<>();
            treeCollect.forEach(item -> {
                String key = pkScenarioScorePrefixKey + item.getTreeCode();
                String value = JSON.toJSONString(item);
                saveScenarioList.add(key);
                saveScenarioList.add(value);
            });
            String treeSet = RedisUtils.mset(jedisPool, indexDb, saveScenarioList.toArray(new String[0]));
            if (!"OK".equals(treeSet)) {
                log.error("保存不包含触发器剧情树计算信息至redis失败!, treeSet:{}", treeSet);
            }
            log.warn("保存不包含触发器剧情树计算信息至redis成功,当前时间:{}", dateTime2String(date));
            log.warn("保存redis设置值: 剧情树key：{}", JSON.toJSONString(saveScenarioList));
        }

    }

    private void save2MySQL(List<Scenario> scenarioList, List<ScenarioPkInfo> scenarioPkInfoList, List<ScenarioRelationTree> scenarioRelationTreeList, Date date) {
        try {
            int BATCH_SIZE = 500;

            if (!CollectionUtils.isEmpty(scenarioPkInfoList)) {
                // Process scenarioPKInfoList in batches
                for (int i = 0; i < scenarioPkInfoList.size(); i += BATCH_SIZE) {
                    int end = Math.min(i + BATCH_SIZE, scenarioPkInfoList.size());
                    List<ScenarioPkInfo> batchList = scenarioPkInfoList.subList(i, end);
                    dataservicePrimaryJdbcTemplateService.saveScenarioPKInfos(batchList, date);
                    log.warn("Saved ScenarioPKInfos batch from index " + i + " to " + (end - 1));
                }
            }

            if (!CollectionUtils.isEmpty(scenarioList)) {
                // Process scenarioList in batches
                for (int i = 0; i < scenarioList.size(); i += BATCH_SIZE) {
                    int end = Math.min(i + BATCH_SIZE, scenarioList.size());
                    List<Scenario> batchList = scenarioList.subList(i, end);
                    dataservicePrimaryJdbcTemplateService.saveScenarioTriggerTypeScore(batchList, date);
                    log.warn("Saved ScenarioTriggerTypeScore batch from index " + i + " to " + (end - 1));
                }
            }

            if (!CollectionUtils.isEmpty(scenarioRelationTreeList)) {
                // Process scenarioRelationTreeList in batches
                for (int i = 0; i < scenarioRelationTreeList.size(); i += BATCH_SIZE) {
                    int end = Math.min(i + BATCH_SIZE, scenarioRelationTreeList.size());
                    List<ScenarioRelationTree> batchList = scenarioRelationTreeList.subList(i, end);
                    dataservicePrimaryJdbcTemplateService.saveScenarioRelationTreeBatch(batchList);
                    log.warn("Saved ScenarioRelationTree batch from index " + i + " to " + (end - 1));
                }
            }
        } catch (Exception e) {
            log.error("保存剧本pk信用分信息到MySQL失败,错误信息:{}", e.getMessage());
            throw new DIYException("保存剧本pk信用分信息到MySQL失败!", e.getCause());
        }
    }


    private static void resetTriggerType(Scenario s) {
        if ("null-triggerType".equals(s.getTriggerType())) {
            s.setTriggerType("");
        }
    }

    @SafeVarargs
    private static <T> java.util.function.Predicate<T> distinctByKey(Function<? super T, ?>... keyExtractors) {
        final Map<List<?>, Boolean> seen = new ConcurrentHashMap<>();
        return t -> {
            final List<?> keys = Arrays.stream(keyExtractors).map(ke -> ke.apply(t)).collect(Collectors.toList());
            return seen.putIfAbsent(keys, Boolean.TRUE) == null;
        };
    }


    @NotNull
    private List<ScenarioInfo> queryAndBuildScenarioObject() {
        //查询剧本
        List<ScenarioInfo> collect = businessPrimaryJdbcTemplateService.queryScenarioInfos().stream().map(item -> {
            ScenarioInfo scenarioInfo = new ScenarioInfo();
            BeanUtils.copyProperties(item, scenarioInfo);
            return scenarioInfo;
        }).toList();

        //设置触发器数量
        List<ScenarioInfo> scenarioInfos = new ArrayList<>();

        Set<String> scenarioCodeSet = collect.stream().map(ScenarioInfo::getScenarioCode).collect(Collectors.toSet());
        List<ScenarioTagInfo> scenarioTagInfoList = dataserviceSlaveJdbcTemplateService.queryTagByScenarioCode(scenarioCodeSet);
        Map<String, List<String>> scenarioTagMap = scenarioTagInfoList.stream()
                .filter(info -> info.getTagCode() != null && !info.getTagCode().isEmpty())
                .collect(Collectors.groupingBy(
                        ScenarioTagInfo::getScenarioCode,
                        Collectors.mapping(ScenarioTagInfo::getTagCode, Collectors.toList())
                ));

        for (ScenarioInfo scenarioInfo : collect) {
            if (StringUtils.isNotBlank(scenarioInfo.getTriggerTypes())) {
                scenarioInfo.setTriggerNum(scenarioInfo.getTriggerTypes().split(ScenarioConstants.JAVA_COMMON_SPLIT_STR).length);
                scenarioInfos.add(scenarioInfo);
            } else {
                if (scenarioTagMap.containsKey(scenarioInfo.getScenarioCode())) {
                    List<String> tags = scenarioTagMap.get(scenarioInfo.getScenarioCode());
                    scenarioInfo.setTriggerTypes(StringUtils.join(tags, ScenarioConstants.JAVA_COMMON_SPLIT_STR));
                    scenarioInfo.setTriggerNum(tags.size());
                    scenarioInfos.add(scenarioInfo);
                } else {
                    scenarioInfo.setTriggerTypes("");
                    scenarioInfo.setTriggerNum(0);
                    scenarioInfos.add(scenarioInfo);
                }
            }
        }

        return scenarioInfos;
    }

    private ScenarioPkInfo calScenarioScores(Double maxAmount, String currentDateTimeStr, ScenarioInfo scenarioInfo) {

        ScenarioPkInfo scenarioPKInfo = ScenarioPkInfo.of(scenarioInfo, dayCreditWeight, scoreCreditWeight, scenarioPriceUpdatedWeightMultiply, scenarioPriceUpdatedWeightAdd, normalPriceWeight, normalWeight, currentDateTimeStr, maxAmount);
        final BigDecimal scenarioAvgValue = scenarioPKInfo.getScenarioTotalCredit();
        scenarioPKInfo.setScenarioTotalWithAvgCredit(scenarioAvgValue);
        scenarioPKInfo.setScenarioDestWithAvgCredit(normalWeight.multiply(BigDecimal.valueOf(new Random().nextGaussian())).add(scenarioAvgValue).setScale(3, RoundingMode.HALF_UP));
        scenarioPKInfo.setId(scenarioInfo.getVin() + "_" + scenarioInfo.getTreeCode() + "_" + scenarioInfo.getScenarioCode());
        scenarioPKInfo.setVin(scenarioInfo.getVin());
        scenarioPKInfo.setTreeCode(StringUtils.isNotBlank(scenarioInfo.getTreeCode()) ? scenarioInfo.getTreeCode() : null);
        scenarioPKInfo.setTreeName(StringUtils.isNotBlank(scenarioInfo.getTreeName()) ? scenarioInfo.getTreeName() : null);
        return scenarioPKInfo;
    }

    @Nullable
    private List<ScenarioPkInfo> calScenarioScores(List<ScenarioInfo> scenarioInfos) {
        if (CollectionUtils.isEmpty(scenarioInfos)) {
            log.warn("query scenario infos failed!");
            return null;
        }
        final Double maxAmount = scenarioInfos.stream().map(ScenarioInfo::getCalAmount).max(Double::compareTo).orElse(0.00);

        //计算剧本P值
        final String currentDateTimeStr = date2String(new Date()) + " 00:00:00";
        final Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DAY_OF_MONTH, -1);

        //拿到剧本信用值
        List<ScenarioPkInfo> scenarioPkInfoList = new ArrayList<>();
        scenarioInfos.forEach(o -> {
            ScenarioPkInfo scenarioPKInfo = calScenarioScores(maxAmount, currentDateTimeStr, o);
            scenarioPkInfoList.add(scenarioPKInfo);
        });
        return scenarioPkInfoList;
    }


    public static String date2String(Date date) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            return dateFormat.format(date);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    private static List<Scenario> getScenarioTriggerInfoList(List<ScenarioInfo> scenarioInfos, List<ScenarioPkInfo> scenarioPkInfoList) {
        List<Scenario> scenarioList = new ArrayList<>();
        scenarioInfos.forEach(obj -> processTriggerTypes(obj, scenarioPkInfoList, scenarioList));
        return scenarioList;
    }


    private static void processTriggerTypes(ScenarioInfo obj, List<ScenarioPkInfo> scenarioPkInfoList, List<Scenario> scenarioList) {
        if (StringUtils.isBlank(obj.getTriggerTypes())) {
            Scenario scenario = Scenario.of(obj, "null-triggerType", scenarioPkInfoList);
            scenario.setId(obj.getVin() + "_" + obj.getTreeCode() + "_" + obj.getScenarioCode());
            scenario.setVin(obj.getVin());
            scenario.setTreeCode(obj.getTreeCode());
            scenario.setTreeName(obj.getTreeName());
            scenarioList.add(scenario);
        } else {
            Arrays.stream(obj.getTriggerTypes().split(ScenarioConstants.JAVA_COMMON_SPLIT_STR))
                    .forEach(value -> createAndAddScenario(obj, value, scenarioPkInfoList, scenarioList));
        }
    }


    private static void createAndAddScenario(ScenarioInfo obj, String value, List<ScenarioPkInfo> scenarioPkInfoList, List<Scenario> scenarioList) {
        Scenario scenario = Scenario.of(obj, value, scenarioPkInfoList);
        scenario.setId(obj.getVin() + "_" + obj.getTreeCode() + "_" + obj.getScenarioCode() + "_" + value);
        scenario.setVin(obj.getVin());
        scenario.setTreeCode(obj.getTreeCode());
        scenario.setTreeName(obj.getTreeName());
        scenarioList.add(scenario);
    }
}
