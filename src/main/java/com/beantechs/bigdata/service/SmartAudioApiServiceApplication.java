package com.beantechs.bigdata.service;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;


@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableApolloConfig
@EnableAsync
@EnableScheduling
@Slf4j
/**
 * <AUTHOR>
 */
public class SmartAudioApiServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(SmartAudioApiServiceApplication.class, args);
        log.warn("==================================project run success=========================================");
    }
}

