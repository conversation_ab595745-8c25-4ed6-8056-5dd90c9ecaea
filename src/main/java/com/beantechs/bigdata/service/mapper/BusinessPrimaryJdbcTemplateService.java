package com.beantechs.bigdata.service.mapper;

import com.beantechs.bigdata.service.entity.business.ScenarioInfoBusiness;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
/**
 * <AUTHOR>
 */
@Repository
public class BusinessPrimaryJdbcTemplateService {


    private final JdbcTemplate jdbcTemplateBusinessPrimary;


    public BusinessPrimaryJdbcTemplateService(@Qualifier("jdbcTemplateBusinessPrimary") JdbcTemplate jdbcTemplateBusinessPrimary) {
        this.jdbcTemplateBusinessPrimary = jdbcTemplateBusinessPrimary;
    }


    public ScenarioInfoBusiness queryScenarioInfosByCode(String code) {
        String sql = """
                SELECT
                    a.id AS scenarioId,
                    a.scenario_code AS scenarioCode,
                    a.state AS scenarioState,
                    a.publish_time AS publishDate,
                    a.validity_start_date AS validityStartDate,
                    a.validity_end_date AS validityEndDate
                FROM
                    scenario_info a
                WHERE
                    a.scenario_code = ?
                """;
        return jdbcTemplateBusinessPrimary.queryForObject(sql, ScenarioInfoBusiness.class, code);
    }


    public List<ScenarioInfoBusiness> queryScenarioInfos() {
        String sql = """
                SELECT
                    *
                FROM
                    (
                    SELECT
                        scenarioInfo.*,
                        tree.tree_name AS treeName,
                        tree.tree_code AS treeCode
                    FROM
                        (
                        SELECT
                            b.tree_code,
                            b.scenario_code,
                            b.tree_name
                        FROM
                            scenario_info a
                        INNER JOIN scenario_tree b ON
                            a.scenario_code = b.scenario_code
                    ) tree
                RIGHT JOIN(
                    SELECT
                        a.id AS scenarioId,
                        a.scenario_code AS scenarioCode,
                        a.scenario_name AS scenarioName,
                        a.trigger_types AS triggerTypes,
                        a.state AS scenarioState,
                        IFNULL(
                            a.publish_time,
                            DATE_SUB(NOW(), INTERVAL 90 DAY)) AS publishDate,
                            IFNULL(
                                a.validity_start_date,
                                DATE_SUB(NOW(), INTERVAL 90 DAY)) AS validityStartDate,
                                a.validity_end_date AS validityEndDate,
                                a.version AS VERSION,
                                IFNULL(b.cal_amount, 0.00) AS calAmount,
                                IFNULL(b.price, 0.00) AS scenarioPrice,
                                a.category AS category
                            FROM
                                scenario_info a
                            LEFT JOIN scenario_amount b ON
                                a.scenario_code = b.scenario_code
                            WHERE
                                a.state IN(1, 2) AND a.category IN(0, 7)
                            ) scenarioInfo
                        ON
                            scenarioInfo.scenarioCode = tree.scenario_code
                        ) result
                    WHERE
                        result.treeCode IS NOT NULL
                """;
        return jdbcTemplateBusinessPrimary.query(sql, new BeanPropertyRowMapper<>(ScenarioInfoBusiness.class));
    }
}
