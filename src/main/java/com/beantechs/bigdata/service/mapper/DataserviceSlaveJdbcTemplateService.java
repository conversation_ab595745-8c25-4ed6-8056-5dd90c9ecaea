package com.beantechs.bigdata.service.mapper;

import com.beantechs.bigdata.service.entity.*;
import com.beantechs.bigdata.service.entity.business.ScenarioTagInfo;
import com.beantechs.bigdata.service.entity.resp.ScenarioStatisticsNumResp;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
/**
 * <AUTHOR>
 */
@Repository
public class DataserviceSlaveJdbcTemplateService {


    private final JdbcTemplate jdbcTemplateDataserviceSlave;

    public DataserviceSlaveJdbcTemplateService(@Qualifier("jdbcTemplateDataserviceSlave") JdbcTemplate jdbcTemplateDataserviceSlave) {
        this.jdbcTemplateDataserviceSlave = jdbcTemplateDataserviceSlave;
    }


    public Scenario queryScenarioMaxDateScoreByCode(String code) {
        String sql = """
                SELECT
                    a.scenario_id,
                    a.scenario_code,
                    a.scenario_total_credit,
                    a.scenario_dest_credit,
                    a.scenario_total_with_avg_credit,
                    a.scenario_dest_with_avg_credit
                FROM
                    (
                    SELECT DISTINCT
                        score.scenario_id,
                        score.scenario_code,
                        score.scenario_total_credit,
                        score.scenario_dest_credit,
                        score.scenario_total_with_avg_credit,
                        score.scenario_dest_with_avg_credit,
                        score.cal_time,
                        TIME.cal_time max_time
                    FROM
                        scenario_trigger_type_score score,
                        (
                        SELECT
                            MAX(cal_time) cal_time
                        FROM
                            scenario_trigger_type_score
                    ) TIME
                WHERE
                    score.scenario_code = ?
                ) a
                WHERE
                    DATE_SUB(a.max_time, INTERVAL 30 DAY) <= a.cal_time
                ORDER BY
                    a.cal_time
                DESC
                LIMIT 1
                """;
        return jdbcTemplateDataserviceSlave.queryForObject(sql, Scenario.class, code);
    }


    public List<KvResp> queryScenarioPkValuesInfoByCodes(List<String> codes) {
        String sql = """
                SELECT
                    c.`key`,
                    c.value
                FROM
                    (
                    SELECT
                        a.scenario_code AS `key`,
                        a.scenario_dest_credit AS
                    VALUE
                FROM
                    scenario_pk_result_info a,
                    (
                    SELECT
                        MAX(cal_time) AS MAX
                    FROM
                        scenario_pk_result_info
                ) b
                WHERE
                    a.cal_time = b.max
                ) c
                WHERE
                    c.`key` IN(
                """;
        sql += String.join(",", Collections.nCopies(codes.size(), "?")) + ")";
        return jdbcTemplateDataserviceSlave.query(sql, new BeanPropertyRowMapper<>(KvResp.class), codes.toArray());
    }


    public List<ScenarioPkInfo> queryPkTreeInfoByVinTree(String vin, List<String> codes) {
        String sql = """
                SELECT
                    *
                FROM
                    scenario_tree_pk_result_info_di
                WHERE
                    vin = ? AND tree_code IN(
                """;
        sql += String.join(",", Collections.nCopies(codes.size(), "?")) + ")";
        List<String> params = new ArrayList<>();
        params.add(vin);
        params.addAll(codes);
        return jdbcTemplateDataserviceSlave.query(sql, new BeanPropertyRowMapper<>(ScenarioPkInfo.class), params.toArray());
    }


    public List<Scenario> queryPkTreeTriggerInfoByVinTree(String vin, List<String> codes) {
        String sql = """
                SELECT
                    *
                FROM
                    scenario_tree_trigger_type_score_di
                WHERE
                    vin = ? AND tree_code IN(
                """;
        sql += String.join(",", Collections.nCopies(codes.size(), "?")) + ")";

        List<String> params = new ArrayList<>();
        params.add(vin);
        params.addAll(codes);
        return jdbcTemplateDataserviceSlave.query(sql, new BeanPropertyRowMapper<>(Scenario.class), params.toArray());

    }


    public AiDwdScenarioServiceCombineRecommendationToMysqlDf getOne(String vin, String beanId, String dt) {
        String sql = """
                SELECT
                    created_at,
                    bean_id,
                    vin,
                    tree_code_ranks,
                    dt
                FROM
                    ai_dwd_scenario_service_combine_recommendation_to_mysql_df
                WHERE
                    vin = ? AND bean_id = ? AND dt = ?
                """;
        try {
            List<String> params = new ArrayList<>();
            params.add(vin);
            params.add(beanId);
            params.add(dt);

            return jdbcTemplateDataserviceSlave.queryForObject(
                    sql,
                    new BeanPropertyRowMapper<>(AiDwdScenarioServiceCombineRecommendationToMysqlDf.class),
                    params.toArray()
            );
        } catch (Exception e) {
            return null;
        }
    }


    public ScenarioTagServiceRecommendationDo getScenarioOne(String vin, String beanId, String dt) {
        String sql = """
                SELECT
                    created_at,
                    bean_id,
                    vin,
                    scenario_code_ranks,
                    dt
                FROM
                    ai_dwd_scenario_tag_service_combine_recommendation_to_mysql_df
                WHERE
                    vin = ? AND bean_id = ? AND dt = ?
                """;
        try {
            List<String> params = new ArrayList<>();
            params.add(vin);
            params.add(beanId);
            params.add(dt);

            return jdbcTemplateDataserviceSlave.queryForObject(
                    sql,
                    new BeanPropertyRowMapper<>(ScenarioTagServiceRecommendationDo.class),
                    params.toArray()
            );
        } catch (Exception e) {
            return null;
        }
    }






    public List<ScenarioStatisticsNumResp> queryScenarioCallNumByDate(String date, String code) {
        String sql = """
                SELECT
                    scenario_code,
                    SUM(call_num) AS call_num,
                    SUM(success_num) AS success_num,
                    action_sign
                FROM
                    scenario_statistics_num
                WHERE
                    call_time RLIKE ? AND scenario_code = ? AND action_sign IS NOT NULL
                GROUP BY
                    action_sign
                """;

        List<String> params = new ArrayList<>();
        params.add(date);
        params.add(code);

        return jdbcTemplateDataserviceSlave.query(sql, new BeanPropertyRowMapper<>(ScenarioStatisticsNumResp.class), params.toArray());
    }


    public List<ScenarioInfo> queryScenarioTreeInfoBatch(Integer num) {
        String sql = """
                SELECT
                    *
                FROM
                    scenario_tree_info
                WHERE
                    id >=(
                    SELECT
                        id
                    FROM
                        scenario_tree_info
                    LIMIT ?,
                    1
                )
                LIMIT 10000
                """;
        return jdbcTemplateDataserviceSlave.query(sql, new BeanPropertyRowMapper<>(ScenarioInfo.class), num);
    }


    public List<ScenarioTagInfo> queryTagByScenarioCode(Set<String> scenarioCodeSet) {
        String sql = """
                SELECT
                    *
                FROM
                    scenario_tag_info
                WHERE
                    scenario_code IN(
                """;
        sql += String.join(",", Collections.nCopies(scenarioCodeSet.size(), "?")) + ")";
        return jdbcTemplateDataserviceSlave.query(sql, new BeanPropertyRowMapper<>(ScenarioTagInfo.class), scenarioCodeSet.toArray());
    }


    public Integer queryScenarioTreeInfoCount() {
        String sql = """
                SELECT
                    COUNT(0)
                FROM
                    scenario_tree_info
                """;
        return jdbcTemplateDataserviceSlave.queryForObject(sql, Integer.class);
    }


}
