package com.beantechs.bigdata.service.mapper;

import com.beantechs.bigdata.service.entity.Scenario;
import com.beantechs.bigdata.service.entity.ScenarioPkInfo;
import com.beantechs.bigdata.service.entity.ScenarioRelationTree;
import com.beantechs.bigdata.service.entity.business.ScenarioTagInfo;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
/**
 * <AUTHOR>
 */
@Repository
public class DataservicePrimaryJdbcTemplateService {


    private final JdbcTemplate jdbcTemplateDataservicePrimary;


    public DataservicePrimaryJdbcTemplateService(@Qualifier("jdbcTemplateDataservicePrimary") JdbcTemplate jdbcTemplateDataservicePrimary) {
        this.jdbcTemplateDataservicePrimary = jdbcTemplateDataservicePrimary;
    }

    public void saveScenarioPKInfos(List<ScenarioPkInfo> scenarioPkInfoList, Date date) {
        String sql = """
                INSERT INTO scenario_pk_result_info(
                    scenario_id,
                    scenario_code,
                    scenario_name,
                    scenario_publish_days,
                    scenario_publish_credit,
                    scenario_max_price,
                    cal_amount,
                    scenario_price,
                    scenario_update_score,
                    scenario_initial_credit,
                    scenario_price_credit,
                    scenario_total_credit,
                    scenario_dest_credit,
                    trigger_num,
                    scenario_state,
                    publish_date,
                    validity_start_date,
                    validity_end_date,
                    cal_time
                )
                VALUES
                """;
        String valuesTemplate = "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        StringBuilder sqlBuilder = new StringBuilder(sql);
        for (int i = 0; i < scenarioPkInfoList.size(); i++) {
            if (i > 0) {
                sqlBuilder.append(", ");
            }
            sqlBuilder.append(valuesTemplate);
        }

        Object[] params = new Object[scenarioPkInfoList.size() * 19];
        for (int i = 0; i < scenarioPkInfoList.size(); i++) {
            ScenarioPkInfo item = scenarioPkInfoList.get(i);
            params[i * 19] = item.getScenarioId();
            params[i * 19 + 1] = item.getScenarioCode();
            params[i * 19 + 2] = item.getScenarioName();
            params[i * 19 + 3] = item.getScenarioPublishDays();
            params[i * 19 + 4] = item.getScenarioPublishCredit();
            params[i * 19 + 5] = item.getScenarioMaxPrice();
            params[i * 19 + 6] = item.getCalAmount();
            params[i * 19 + 7] = item.getScenarioPrice();
            params[i * 19 + 8] = item.getScenarioUpdateScore();
            params[i * 19 + 9] = item.getScenarioInitialCredit();
            params[i * 19 + 10] = item.getScenarioPriceCredit();
            params[i * 19 + 11] = item.getScenarioTotalCredit();
            params[i * 19 + 12] = item.getScenarioDestCredit();
            params[i * 19 + 13] = item.getTriggerNum();
            params[i * 19 + 14] = item.getScenarioState();
            params[i * 19 + 15] = item.getPublishDate();
            params[i * 19 + 16] = item.getValidityStartDate();
            params[i * 19 + 17] = item.getValidityEndDate();
            params[i * 19 + 18] = date;
        }
        jdbcTemplateDataservicePrimary.update(sqlBuilder.toString(), params);
    }


    public void saveScenarioTriggerTypeScore(List<Scenario> scenarioList, Date date) {
        String sql = """
                INSERT INTO scenario_trigger_type_score(
                    scenario_id,
                    scenario_code,
                    scenario_name,
                    trigger_type,
                    trigger_num,
                    scenario_total_credit,
                    scenario_dest_credit,
                    scenario_total_with_avg_credit,
                    scenario_dest_with_avg_credit,
                    cal_time
                )
                VALUES
                """;

        StringBuilder sqlBuilder = new StringBuilder(sql);
        String valuesTemplate = "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        for (int i = 0; i < scenarioList.size(); i++) {
            if (i > 0) {
                sqlBuilder.append(", ");
            }
            sqlBuilder.append(valuesTemplate);
        }

        Object[] params = new Object[scenarioList.size() * 10];
        for (int i = 0; i < scenarioList.size(); i++) {
            Scenario item = scenarioList.get(i);
            params[i * 10] = item.getScenarioId();
            params[i * 10 + 1] = item.getScenarioCode();
            params[i * 10 + 2] = item.getScenarioName();
            params[i * 10 + 3] = item.getTriggerType();
            params[i * 10 + 4] = item.getTriggerNum();
            params[i * 10 + 5] = item.getScenarioTotalCredit();
            params[i * 10 + 6] = item.getScenarioDestCredit();
            params[i * 10 + 7] = item.getScenarioTotalWithAvgCredit();
            params[i * 10 + 8] = item.getScenarioDestWithAvgCredit();
            params[i * 10 + 9] = date;
        }
        jdbcTemplateDataservicePrimary.update(sqlBuilder.toString(), params);
    }


    public void saveScenarioRelationTreeBatch(List<ScenarioRelationTree> scenarioRelationTreeList) {
        String sql = """
                REPLACE
                INTO scenario_relation_scenario_tree_df(
                    id,
                    scenario_name,
                    scenario_code,
                    tree_name,
                    tree_code
                )
                VALUES
                """;

        StringBuilder sqlBuilder = new StringBuilder(sql);
        String valuesTemplate = "(?, ?, ?, ?, ?)";

        for (int i = 0; i < scenarioRelationTreeList.size(); i++) {
            if (i > 0) {
                sqlBuilder.append(", ");
            }
            sqlBuilder.append(valuesTemplate);
        }

        Object[] params = new Object[scenarioRelationTreeList.size() * 5];
        for (int i = 0; i < scenarioRelationTreeList.size(); i++) {
            ScenarioRelationTree item = scenarioRelationTreeList.get(i);
            params[i * 5] = item.getId();
            params[i * 5 + 1] = item.getScenarioName();
            params[i * 5 + 2] = item.getScenarioCode();
            params[i * 5 + 3] = item.getTreeName();
            params[i * 5 + 4] = item.getTreeCode();
        }
        jdbcTemplateDataservicePrimary.update(sqlBuilder.toString(), params);
    }


}
