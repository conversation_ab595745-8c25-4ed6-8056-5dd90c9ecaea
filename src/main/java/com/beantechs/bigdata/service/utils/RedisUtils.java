package com.beantechs.bigdata.service.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.*;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class RedisUtils {
    private static final Logger log = LoggerFactory.getLogger(RedisUtils.class);

    public static String get(String key, int indexDb, JedisPool jedisPool) {
        Jedis jedis = null;
        String value = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(indexDb);
            value = jedis.get(key);
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            returnResource(jedisPool, jedis);
        }

        return value;
    }

    public static byte[] get(byte[] key, int indexDb, JedisPool jedisPool) {
        Jedis jedis = null;
        byte[] value = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(indexDb);
            value = jedis.get(key);
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            returnResource(jedisPool, jedis);
        }

        return value;
    }


    public static Set<String> scan(String pattern, int database, JedisPool jedisPool, Integer count) {
        Jedis jedis = null;
        Set<String> res = new HashSet();

        try {
            jedis = jedisPool.getResource();
            jedis.select(database);
            String cursor = "0";

            do {
                ScanResult<String> ret = jedis.scan(cursor, (new ScanParams()).match(pattern).count(count));
                cursor = ret.getCursor();
                res.addAll(ret.getResult());
            } while (!"0".equals(cursor));
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            returnResource(jedisPool, jedis);
        }

        return res;
    }



    public static Long del(JedisPool jedisPool, int indexDb, String... keys) {
        Jedis jedis = null;

        Long var5;
        try {
            jedis = jedisPool.getResource();
            jedis.select(indexDb);
            Long var4 = jedis.del(keys);
            return var4;
        } catch (Exception e) {
            log.error(e.getMessage());
            var5 = 0L;
        } finally {
            returnResource(jedisPool, jedis);
        }

        return var5;
    }



    public static Long append(String key, String str, JedisPool jedisPool) {
        Jedis jedis = null;
        Long res = null;

        Long var6;
        try {
            jedis = jedisPool.getResource();
            res = jedis.append(key, str);
            return res;
        } catch (Exception e) {
            log.error(e.getMessage());
            var6 = 0L;
        } finally {
            returnResource(jedisPool, jedis);
        }

        return var6;
    }


    public static Long expire(String key, int value, int indexDb, JedisPool jedisPool) {
        Jedis jedis = null;

        Long var6;
        try {
            jedis = jedisPool.getResource();
            jedis.select(indexDb);
            Long var5 = jedis.expire(key, value);
            return var5;
        } catch (Exception e) {
            log.error(e.getMessage());
            var6 = 0L;
        } finally {
            returnResource(jedisPool, jedis);
        }

        return var6;
    }


    public static Long setnx(String key, String value, int indexDb, JedisPool jedisPool) {
        Jedis jedis = null;

        Long var6;
        try {
            jedis = jedisPool.getResource();
            jedis.select(indexDb);
            Long var5 = jedis.setnx(key, value);
            return var5;
        } catch (Exception e) {
            log.error(e.getMessage());
            var6 = 0L;
        } finally {
            returnResource(jedisPool, jedis);
        }

        return var6;
    }


    public static String mset(JedisPool jedisPool, int database, String... keysvalues) {
        Jedis jedis = null;
        String res = null;

        try {
            jedis = jedisPool.getResource();
            jedis.select(database);
            res = jedis.mset(keysvalues);
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            returnResource(jedisPool, jedis);
        }

        return res;
    }


    public static String type(String key, JedisPool jedisPool) {
        Jedis jedis = null;
        String res = null;

        try {
            jedis = jedisPool.getResource();
            res = jedis.type(key);
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            returnResource(jedisPool, jedis);
        }

        return res;
    }


    public static void returnResource(JedisPool jedisPool, Jedis jedis) {
        if (jedis != null) {
            jedisPool.returnResource(jedis);
        }

    }
}
