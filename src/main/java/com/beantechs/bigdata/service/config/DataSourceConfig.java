package com.beantechs.bigdata.service.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 */
@Configuration
public class DataSourceConfig {

    @Value("${spring.datasource.dynamic.datasource.dataservice.url}")
    private String dataserviceJdbcUrl;
    @Value("${spring.datasource.dynamic.datasource.dataservice.username}")
    private String dataserviceUsername;
    @Value("${spring.datasource.dynamic.datasource.dataservice.password}")
    private String dataservicePassword;

    @Value("${spring.datasource.dynamic.datasource.slave.url}")
    private String slaveJdbcUrl;
    @Value("${spring.datasource.dynamic.datasource.slave.username}")
    private String slaveUsername;
    @Value("${spring.datasource.dynamic.datasource.slave.password}")
    private String slavePassword;

    @Value("${spring.datasource.dynamic.datasource.business.url}")
    private String businessJdbcUrl;
    @Value("${spring.datasource.dynamic.datasource.business.username}")
    private String businessUsername;
    @Value("${spring.datasource.dynamic.datasource.business.password}")
    private String businessPassword;


    @Bean(name = "dataservicePrimary")
    public DataSource dataservicePrimary() {
        return buildHikariDataSource(
                dataserviceJdbcUrl,
                dataserviceUsername,
                dataservicePassword,
                "dataservicePrimary"
        );
    }

    @Bean(name = "dataserviceSlave")
    public DataSource dataserviceSlave() {
        return buildHikariDataSource(
                slaveJdbcUrl,
                slaveUsername,
                slavePassword,
                "dataserviceSlave"
        );
    }

    @Bean(name = "businessPrimary")
    public DataSource businessPrimary() {
        return buildHikariDataSource(
                businessJdbcUrl,
                businessUsername,
                businessPassword,
                "businessPrimary"
        );
    }

    @Bean(name = "jdbcTemplateDataservicePrimary")
    public JdbcTemplate jdbcTemplateDataservicePrimary(@Qualifier("dataservicePrimary") DataSource dataservicePrimary) {
        return new JdbcTemplate(dataservicePrimary);
    }

    @Bean(name = "jdbcTemplateDataserviceSlave")
    public JdbcTemplate jdbcTemplateDataserviceSlave(@Qualifier("dataserviceSlave") DataSource dataserviceSlave) {
        return new JdbcTemplate(dataserviceSlave);
    }

    @Bean(name = "jdbcTemplateBusinessPrimary")
    public JdbcTemplate jdbcTemplateBusinessPrimary(@Qualifier("businessPrimary") DataSource businessPrimary) {
        return new JdbcTemplate(businessPrimary);
    }


    private static DataSource buildHikariDataSource(String jdbcUrl, String username, String password, String dbName) {
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl(jdbcUrl);
        hikariConfig.setUsername(username);
        hikariConfig.setPassword(password);
        hikariConfig.setDriverClassName("com.mysql.cj.jdbc.Driver");
        hikariConfig.setMaximumPoolSize(20);
        hikariConfig.setMinimumIdle(10);
        hikariConfig.setIdleTimeout(30000);
        hikariConfig.setMaxLifetime(600000);
        hikariConfig.setConnectionTimeout(30000);
        hikariConfig.setPoolName("HikariCP-" + dbName);
        return new HikariDataSource(hikariConfig);
    }


}
