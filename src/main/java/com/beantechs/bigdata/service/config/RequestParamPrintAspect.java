package com.beantechs.bigdata.service.config;

import com.alibaba.fastjson.JSON;
import com.beantechs.service.ResBody.ResponseEntity;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Aspect
@Component
@Order(-1)
@Slf4j
public class RequestParamPrintAspect {


    @Around("within(com.beantechs.bigdata.service.controller.*)")
    public Object doAround(ProceedingJoinPoint joinPoint) {
        final ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null == ra) {
            log.error("请求参数异常, 参数为空");
        }
        assert ra != null;
        final HttpServletRequest request = ra.getRequest();
        final String paramsMapStr = getParamsMapStr(request);

        Object result;
        try {
            result = joinPoint.proceed();
        } catch (Throwable throwable) {
            return getResponseEntityError(joinPoint, request, paramsMapStr, throwable);
        }
        String msg = JSON.toJSONString(result);
        printApiResultMsg(request, joinPoint, paramsMapStr, msg);
        return result;
    }

    private static ResponseEntity<Object> getResponseEntityError(ProceedingJoinPoint joinPoint, HttpServletRequest request, String paramsMapStr, Throwable throwable) {
        log.error("接口执行中错误信息:{}", getStackTrace(throwable));
        ResponseEntity<Object> objectResponseEntity = ResponseEntity.responseByErrorMsg("系统异常!");
        printApiResultMsg(request, joinPoint, paramsMapStr, JSON.toJSONString(objectResponseEntity));
        return objectResponseEntity;
    }

    /**
     * 输出结果信息
     */
    private static void printApiResultMsg(HttpServletRequest request, ProceedingJoinPoint joinPoint, String paramsMapStr, String msg) {
        log.warn("请求来源:  => {}, 请求URL: {}, 响应方法: {}, 请求参数:{}, 请求体:{}, 返回结果:{}", request.getRemoteAddr(), request.getRequestURL().toString(), joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName(), paramsMapStr, JSON.toJSONString(joinPoint.getArgs()), msg);
    }


    /**
     * 获取入参信息
     */
    private static String getParamsMapStr(HttpServletRequest request) {
        Map<String, String> parameterMap = new HashMap<>();
        Enumeration<String> enumeration = request.getParameterNames();
        while (enumeration.hasMoreElements()) {
            String name = enumeration.nextElement();
            String value = request.getParameter(name);
            parameterMap.put(name, value);
        }
        return JSON.toJSONString(parameterMap);
    }


    /**
     * 输出报错日志
     */
    public static String getStackTrace(Throwable throwable) {
        StringWriter sw = new StringWriter();
        try (PrintWriter pw = new PrintWriter(sw)) {
            throwable.printStackTrace(pw);
            return sw.toString();
        }
    }
}
