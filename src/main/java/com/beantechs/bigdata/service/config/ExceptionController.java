package com.beantechs.bigdata.service.config;

import com.alibaba.fastjson.JSON;
import com.beantechs.service.ResBody.ResponseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@ControllerAdvice
@Slf4j
public class ExceptionController {


    @ResponseBody
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public ResponseEntity<String> handleMethodArgumentNotValidException(MethodArgumentNotValidException exception) {
        log.error("exception:{}", JSON.toJSONString(exception.getBindingResult().getAllErrors()));
        BindingResult bindingResult = exception.getBindingResult();
        List<String> collect = null;
        if (bindingResult.hasErrors()) {
            List<FieldError> fieldErrors = bindingResult.getFieldErrors();
            collect = fieldErrors.stream().map(FieldError::getDefaultMessage).collect(Collectors.toList());
        }
        return ResponseEntity.responseByErrorMsg("request param check error,error msg list:" + JSON.toJSONString(Optional.ofNullable(collect).orElse(new ArrayList<>())));
    }


}
