package com.beantechs.bigdata.service.controller;


import com.beantechs.bigdata.service.config.EnvironmentJudge;
import com.beantechs.bigdata.service.config.ScenarioConstants;
import com.beantechs.bigdata.service.entity.resp.ScenarioStatisticsNumResp;
import com.beantechs.bigdata.service.handle.AsyncExecuteThreadPoolHandle;
import com.beantechs.bigdata.service.handle.CalculateHandle;
import com.beantechs.service.ResBody.ResponseEntity;
import com.beantechs.service.exception.DIYException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Conditional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/another")
@Slf4j
@Conditional(EnvironmentJudge.class)
/**
 * <AUTHOR>
 */
public class AnotherController {

    private final CalculateHandle calculateHandle;

    private final AsyncExecuteThreadPoolHandle asyncExecuteThreadPoolHandle;

    public AnotherController(CalculateHandle calculateHandle, AsyncExecuteThreadPoolHandle asyncExecuteThreadPoolHandle) {
        this.calculateHandle = calculateHandle;
        this.asyncExecuteThreadPoolHandle = asyncExecuteThreadPoolHandle;
    }


    @GetMapping("/scenarioStatistics")
    public ResponseEntity<ScenarioStatisticsNumResp> scenarioStatistics(@RequestParam(value = "code") String code) {
        ScenarioStatisticsNumResp scenarioStatisticsNum;
        try {
            scenarioStatisticsNum = calculateHandle.queryScenarioStatisticsNum(code);
        } catch (DIYException e) {
            return ResponseEntity.responseByErrorMsg(e.getMessage());
        }
        if (null == scenarioStatisticsNum) {
            return ResponseEntity.responseBySucceed("can not find msg for code:" + code);
        }
        return ResponseEntity.responseBySucceedData(ScenarioConstants.COMMON_SERVICE_STRING_SUCCESS, scenarioStatisticsNum);
    }


    @GetMapping("/manual/update")
    public ResponseEntity<ScenarioStatisticsNumResp> manualUpdateScenarioTree() {
        Boolean b = asyncExecuteThreadPoolHandle.queryAndCalculateScenario();
        if (b) {
            return ResponseEntity.responseBySucceed("同步成功!");
        } else {
            return ResponseEntity.responseBySucceed("同步失败!");
        }
    }


}
