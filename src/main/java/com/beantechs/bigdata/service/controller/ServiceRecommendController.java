package com.beantechs.bigdata.service.controller;


import com.alibaba.fastjson.JSONObject;
import com.beantechs.bigdata.service.entity.req.ScenarioRecommendReq;
import com.beantechs.bigdata.service.entity.resp.ServiceRecommendResp;
import com.beantechs.bigdata.service.handle.ServiceRecommendHandle;
import com.beantechs.service.ResBody.ResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
@Slf4j
@Tag(name = "PKAPI", description = "PK相关接口")

public class ServiceRecommendController {


    private final ServiceRecommendHandle serviceRecommendHandle;

    public ServiceRecommendController(ServiceRecommendHandle serviceRecommendHandle) {
        this.serviceRecommendHandle = serviceRecommendHandle;
    }


    /**
     * 场景标签服务推荐接口
     *
     * @param req 请求体
     * @return 响应体
     */
    @PostMapping("/service/recommend")
    @Operation(summary = "服务推荐接口", description = "服务推荐接口-v6-场景标签服务推荐")
    @ApiResponse(description = "服务推荐接口响应体", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseEntity.class)))
    public ResponseEntity<ServiceRecommendResp> serviceRecommend(
            @Parameter(schema = @Schema(implementation = ScenarioRecommendReq.class), required = true)
            @RequestBody ScenarioRecommendReq req
    ) {
        return serviceRecommendHandle.serviceRecommendHandle(req);
    }


    /**
     * 场景标签服务推荐接口
     *
     * @param req 请求体
     * @return 响应体
     */
    @PostMapping("/service/recommend/test")
    @Operation(summary = "服务推荐接口", description = "服务推荐测试接口-v6-场景标签服务推荐")
    @ApiResponse(description = "服务推荐接口响应体", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponseEntity.class)))
    public ResponseEntity<JSONObject> serviceRecommendTest(
            @Parameter(schema = @Schema(implementation = ScenarioRecommendReq.class), required = true)
            @RequestBody ScenarioRecommendReq req
    ) {
        return serviceRecommendHandle.serviceRecommendHandleTest(req);
    }


}
