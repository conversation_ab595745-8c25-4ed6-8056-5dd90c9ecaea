package com.beantechs.bigdata.service.entity;

import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ScenarioInfo implements Serializable {
    @Column("id")
    private String id;

    @Column("tree_name")
    private String treeName;

    @Column("tree_code")
    private String treeCode;

    @Column("vin")
    private String vin;

    @Column("scenario_id")
    private Integer scenarioId;

    @Column("scenario_code")
    private String scenarioCode;

    @Column("scenario_name")
    private String scenarioName;

    @Column("trigger_types")
    private String triggerTypes;

    @Column("trigger_num")
    private Integer triggerNum;

    @Column("scenario_state")
    private Integer scenarioState;

    @Column("publish_date")
    private Date publishDate;

    @Column("validity_start_date")
    private Date validityStartDate;

    @Column("validity_end_date")
    private Date validityEndDate;

    @Column("cal_amount")
    private double calAmount;

    @Column("scenario_price")
    private BigDecimal scenarioPrice;

    @Column("update_time")
    private String updateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
}
