package com.beantechs.bigdata.service.entity.business;


import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;
/**
 * <AUTHOR>
 */
@Data
public class ScenarioTagInfo {

    @Column("id")
    private Long id;

    @Column("scenario_code")
    private String scenarioCode;

    @Column("tag_code")
    private String tagCode;

    @Column("create_by")
    private String createBy;

    @Column("create_time")
    private String createTime;

    @Column("update_by")
    private String updateBy;

    @Column("update_time")
    private String updateTime;

    @Column("is_delete")
    private Integer isDelete;
}