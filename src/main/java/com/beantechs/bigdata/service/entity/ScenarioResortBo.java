package com.beantechs.bigdata.service.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScenarioResortBo implements Serializable {

    private String scenarioCode;

    private Integer num;

    private Integer rank;

    private double resultPvalue;


    public ScenarioResortBo(String scenarioCode, Integer num, Integer rank) {
        this.scenarioCode = scenarioCode;
        this.num = num;
        this.rank = rank;
    }
}
