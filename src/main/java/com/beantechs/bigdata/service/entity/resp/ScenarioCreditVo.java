package com.beantechs.bigdata.service.entity.resp;

import com.beantechs.bigdata.service.entity.Scenario;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;
/**
 * <AUTHOR>
 */
@Data
public class ScenarioCreditVo implements Serializable {

    private String scenarioCode;

    private String scenarioName;

    private Double feedbackCredit;

    private Double scenarioTotalCredit;

    private Double scenarioDestCredit;

    private String treeCode;

    public static ScenarioCreditVo from(Scenario scenario) {
        ScenarioCreditVo vo = new ScenarioCreditVo();
        if (Objects.isNull(scenario.getFeedbackCredit())) {
            vo.feedbackCredit = 0.0;
        } else {
            vo.feedbackCredit = scenario.getFeedbackCredit().doubleValue();
        }
        vo.scenarioCode = scenario.getScenarioCode();
        vo.scenarioName = scenario.getScenarioName();
        vo.scenarioTotalCredit = scenario.getScenarioTotalCredit().doubleValue();
        vo.scenarioDestCredit = scenario.getScenarioDestCredit().doubleValue();
        return vo;
    }


    public static ScenarioCreditVo of(Scenario scenario) {
        ScenarioCreditVo scenarioCreditVo = new ScenarioCreditVo();
        scenarioCreditVo.setScenarioCode(scenario.getScenarioCode());
        scenarioCreditVo.setScenarioName(scenario.getScenarioName());
        scenarioCreditVo.setScenarioTotalCredit(scenario.getScenarioTotalCredit().doubleValue());
        scenarioCreditVo.setScenarioDestCredit(scenario.getScenarioDestCredit().doubleValue());
        return scenarioCreditVo;
    }


}
