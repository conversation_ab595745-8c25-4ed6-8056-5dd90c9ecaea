package com.beantechs.bigdata.service.entity;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ScenarioRelationTree {

    private String id;

    private String scenarioName;

    private String scenarioCode;

    private String treeName;

    private String treeCode;


    public static ScenarioRelationTree of(ScenarioPkInfo item) {
        return new ScenarioRelationTree() {{
            setId(item.getScenarioCode() + "_" + item.getTreeCode());
            setScenarioName(item.getScenarioName());
            setScenarioCode(item.getScenarioCode());
            setTreeName(item.getTreeName());
            setTreeCode(item.getTreeCode());
        }};
    }

}