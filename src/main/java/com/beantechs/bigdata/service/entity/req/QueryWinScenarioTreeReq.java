package com.beantechs.bigdata.service.entity.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;
/**
 * <AUTHOR>
 */
@Data
public class QueryWinScenarioTreeReq implements Serializable {

    @NotBlank(message = "vin不可为空")
    private String vin;

    private String beanId;

    private Integer actionSign;

    private List<String> treeCodes;

    private List<String> queryTreeScoreList;
}
