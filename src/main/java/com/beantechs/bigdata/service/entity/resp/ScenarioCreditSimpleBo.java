package com.beantechs.bigdata.service.entity.resp;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
/**
 * <AUTHOR>
 */
@Data
public class ScenarioCreditSimpleBo implements Serializable {

    private String code;

    private BigDecimal total;

    private BigDecimal dest;

    private ScenarioCreditSimpleBo(String code, BigDecimal total, BigDecimal dest) {
        this.code = code;
        this.total = total;
        this.dest = dest;
    }

    public static ScenarioCreditSimpleBo create(String code, BigDecimal total, BigDecimal dest) {
        return new ScenarioCreditSimpleBo(code, total, dest);
    }

}
