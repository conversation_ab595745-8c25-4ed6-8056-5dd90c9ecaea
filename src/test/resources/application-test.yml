server:
  port: 8080
  undertow:
    io-threads: 16
    worker-threads: 256
    buffer-size: 1024
    direct-buffers: true

spring:
  application:
    name: smart-audio-api-service
  datasource:
    dynamic:
      datasource:
        dataservice:
          url: ******************************************************************************************************************************
          username: test_user
          password: test_password
        slave:
          url: ************************************************************************************************************************************
          username: test_user
          password: test_password
        business:
          url: ***************************************************************************************************************************
          username: test_user
          password: test_password
  redis:
    host: localhost
    port: 6379
    password:
    database: 1
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      retries: 3
      batch-size: 16384
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      group-id: smart-audio-consumer-group
      auto-offset-reset: earliest
      enable-auto-commit: true
      auto-commit-interval: 1000
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer

kafka:
  topics:
    scenario-query-record: scenario-query-record-test

thread-pool:
  core-pool-size: 10
  max-pool-size: 20
  queue-capacity: 100
  keep-alive-seconds: 60
  thread-name-prefix: async-service-

cache:
  service-recommend:
    expire-time: 3600
  scenario-pk:
    expire-time: 3600

springdoc:
  api-docs:
    enabled: false
