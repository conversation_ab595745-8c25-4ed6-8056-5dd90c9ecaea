package com.beantechs.bigdata.service.handle;

import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * AsyncExecuteThreadPoolHandle的单元测试类
 */
public class AsyncExecuteThreadPoolHandleTest {

    @Mock
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;


    @BeforeEach
    public void setup() {
        // 初始化Mockito注解
        MockitoAnnotations.openMocks(this);

        // 模拟线程池的执行
        when(threadPoolTaskExecutor.submit(any(Runnable.class))).thenAnswer(invocation -> {
            Runnable runnable = invocation.getArgument(0);
            runnable.run(); // 直接执行任务，而不是异步执行
            return null;
        });
    }


}