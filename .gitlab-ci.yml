variables:
  APP_NAME: "smart-audio-api-service"
  APP_VERSION: "v1.0"

stages:
  - package
  - build
  - deploy_gw_qa
  - deploy_gw_sit
  - deploy_gw_prd
  - done


maven_package:
  stage: package
  image: maven:3.8-openjdk-17
  tags:
    - docker-runner
  before_script:
    - echo "===============  开始编译打包任务  ==============="
    - rm -rf ${ARTIFACTS_PATH} && mkdir ${ARTIFACTS_PATH}
  script:
    - mvn --settings /home/<USER>/.m2/settings.xml clean package -DskipTests
  after_script:
    - cp Dockerfile ${ARTIFACTS_PATH}
    - cp target/${APP_NAME}.jar ${ARTIFACTS_PATH}


# 定义任务：构建镜像
docker_build:
  stage: build
  image: docker:20.10.5
  tags:
    - docker-runner
  only:
    - develop
    - master
  before_script:
    - echo "=============== 开始构建docker镜像任务  ==============="
  script:
    - cp ${ARTIFACTS_PATH}/${APP_NAME}.jar ./
    - echo "===================  当前目录  ======================="
    - pwd
    - docker build -t ${GW_ARTIFACT_IMAGE} .
    - docker push ${GW_ARTIFACT_IMAGE}

k8s_deploy_gw_qa:
  image: alpine:3.13
  stage: deploy_gw_qa
  variables:
    BASE64_KUBE_CONFIG: ${GW_QA_KUBE_CONFIG}
    ENV: "qa"
    REPLICAS: 1
    RESOURCES_CPU: 100m
    RESOURCES_MEMORY: 200Mi
    RESOURCES_LIMIT_CPU: 1000m
    RESOURCES_LIMIT_MEMORY: 2G
    K8S_API_VERSION: extensions/v1beta1
    SKYWALKING_IMAGE: ************:5000/skywalking-java-agent:8.11.0-alpine
  tags:
    - docker-runner
  only:
    - develop
  when: manual
  before_script:
    - echo "===============    开始初始化Kubernetes配置    ==============="
    - chmod +x /home/<USER>/runner-bin/kubectl
    - ln -s /home/<USER>/runner-bin/kubectl /usr/bin/kubectl
    - rm -rf ${KUBE_HOME} && mkdir ${KUBE_HOME} && touch ${KUBE_CONFIG}
    - echo ${BASE64_KUBE_CONFIG} |base64 -d > ${KUBE_CONFIG}
    - kubectl version
  script:
    - echo "===============  开始发布Kubernetes Deployment任务  ==============="
    - IMAGE=`echo $GW_ARTIFACT_IMAGE | sed 's#\/#\\\/#g'`
    - K8S_API_VERSION=`echo $K8S_API_VERSION | sed 's#\/#\\\/#g'`
    - SKYWALKING_IMAGE=`echo $SKYWALKING_IMAGE | sed 's#\/#\\\/#g'`
    - API_HEALTH_CHECK_PATH=`echo $API_HEALTH_CHECK_PATH | sed 's#\/#\\\/#g'`
    - sed -i "s/DOCKER_IMAGE/${IMAGE}/g" deploy.yaml
    - sed -i "s/ACTIVE_PROFILE/${ENV}/g"  ./deploy.yaml
    - sed -i "s/REPLICASNUM/${REPLICAS}/g"  ./deploy.yaml
    - sed -i "s/RESOURCES_CPU/${RESOURCES_CPU}/g"  ./deploy.yaml
    - sed -i "s/RESOURCES_MEMORY/${RESOURCES_MEMORY}/g"  ./deploy.yaml
    - sed -i "s/RESOURCES_LIMIT_CPU/${RESOURCES_LIMIT_CPU}/g"  ./deploy.yaml
    - sed -i "s/RESOURCES_LIMIT_MEMORY/${RESOURCES_LIMIT_MEMORY}/g"  ./deploy.yaml
    - sed -i "s/HEALTH_CHECK/${API_HEALTH_CHECK_PATH}/g"  ./deploy.yaml
    - sed -i "s/K8S_API_VERSION/${K8S_API_VERSION}/g"  ./deploy.yaml
    - sed -i "s/SKYWALKING_IMAGE/${SKYWALKING_IMAGE}/g"  ./deploy.yaml
    - cat deploy.yaml
    - kubectl apply -f deploy.yaml --record=true



k8s_deploy_sit:
  image: alpine:3.13
  stage: deploy_gw_sit
  variables:
    BASE64_KUBE_CONFIG: ${GW_SIT_KUBE_CONFIG}
    ENV: "sit"
    REPLICAS: 1
    RESOURCES_CPU: 1000m
    RESOURCES_MEMORY: 2G
    RESOURCES_LIMIT_CPU: 1000m
    RESOURCES_LIMIT_MEMORY: 2G
    K8S_API_VERSION: extensions/v1beta1
    SKYWALKING_IMAGE: ************:5000/skywalking-java-agent:8.11.0-alpine
  tags:
    - docker-runner
  only:
    - develop
  when: manual
  before_script:
    - echo "===============    开始初始化Kubernetes配置    ==============="
    - chmod +x /home/<USER>/runner-bin/kubectl
    - ln -s /home/<USER>/runner-bin/kubectl /usr/bin/kubectl
    - rm -rf ${KUBE_HOME} && mkdir ${KUBE_HOME} && touch ${KUBE_CONFIG}
    - echo ${BASE64_KUBE_CONFIG} |base64 -d > ${KUBE_CONFIG}
    - kubectl version
  script:
    - echo "===============  开始发布Kubernetes Deployment任务  ==============="
    - IMAGE=`echo $GW_ARTIFACT_IMAGE | sed 's#\/#\\\/#g'`
    - SKYWALKING_IMAGE=`echo $SKYWALKING_IMAGE | sed 's#\/#\\\/#g'`
    - K8S_API_VERSION=`echo $K8S_API_VERSION | sed 's#\/#\\\/#g'`
    - API_HEALTH_CHECK_PATH=`echo $API_HEALTH_CHECK_PATH | sed 's#\/#\\\/#g'`
    - sed -i "s/DOCKER_IMAGE/${IMAGE}/g" deploy.yaml
    - sed -i "s/REPLICASNUM/${REPLICAS}/g"  ./deploy.yaml
    - sed -i "s/RESOURCES_CPU/${RESOURCES_CPU}/g"  ./deploy.yaml
    - sed -i "s/ACTIVE_PROFILE/${ENV}/g"  ./deploy.yaml
    - sed -i "s/RESOURCES_MEMORY/${RESOURCES_MEMORY}/g"  ./deploy.yaml
    - sed -i "s/RESOURCES_LIMIT_CPU/${RESOURCES_LIMIT_CPU}/g"  ./deploy.yaml
    - sed -i "s/RESOURCES_LIMIT_MEMORY/${RESOURCES_LIMIT_MEMORY}/g"  ./deploy.yaml
    - sed -i "s/HEALTH_CHECK/${API_HEALTH_CHECK_PATH}/g"  ./deploy.yaml
    - sed -i "s/K8S_API_VERSION/${K8S_API_VERSION}/g"  ./deploy.yaml
    - sed -i "s/SKYWALKING_IMAGE/${SKYWALKING_IMAGE}/g"  ./deploy.yaml
    - cat deploy.yaml
    - kubectl apply -f deploy.yaml --record=true


k8s_deploy_prd:
  image: alpine:3.13
  stage: deploy_gw_prd
  variables:
    BASE64_KUBE_CONFIG: ${GW_PRD_KUBE_CONFIG}
    ENV: "prd"
    REPLICAS: 3
    RESOURCES_CPU: 1000m
    RESOURCES_MEMORY: 2G
    RESOURCES_LIMIT_CPU: 2000m
    RESOURCES_LIMIT_MEMORY: 3G
    K8S_API_VERSION: apps/v1
    SKYWALKING_IMAGE: ************:5000/skywalking-java-agent:8.11.0-alpine
  tags:
    - docker-runner
  only:
    - master
  when: always
  before_script:
    - echo "===============    开始初始化Kubernetes配置    ==============="
    - chmod +x /home/<USER>/runner-bin/kubectl
    - ln -s /home/<USER>/runner-bin/kubectl /usr/bin/kubectl
    - rm -rf ${KUBE_HOME} && mkdir ${KUBE_HOME} && touch ${KUBE_CONFIG}
    - echo ${BASE64_KUBE_CONFIG} |base64 -d > ${KUBE_CONFIG}
    - kubectl version
  script:
    - echo "===============  开始发布Kubernetes Deployment任务  ==============="
    - IMAGE=`echo $GW_ARTIFACT_IMAGE | sed 's#\/#\\\/#g'`
    - K8S_API_VERSION=`echo $K8S_API_VERSION | sed 's#\/#\\\/#g'`
    - SKYWALKING_IMAGE=`echo $SKYWALKING_IMAGE | sed 's#\/#\\\/#g'`
    - API_HEALTH_CHECK_PATH=`echo $API_HEALTH_CHECK_PATH | sed 's#\/#\\\/#g'`
    - sed -i "s/DOCKER_IMAGE/${IMAGE}/g" deploy.yaml
    - sed -i "s/REPLICASNUM/${REPLICAS}/g"  ./deploy.yaml
    - sed -i "s/ACTIVE_PROFILE/${ENV}/g"  ./deploy.yaml
    - sed -i "s/RESOURCES_CPU/${RESOURCES_CPU}/g"  ./deploy.yaml
    - sed -i "s/RESOURCES_MEMORY/${RESOURCES_MEMORY}/g"  ./deploy.yaml
    - sed -i "s/RESOURCES_LIMIT_CPU/${RESOURCES_LIMIT_CPU}/g"  ./deploy.yaml
    - sed -i "s/RESOURCES_LIMIT_MEMORY/${RESOURCES_LIMIT_MEMORY}/g"  ./deploy.yaml
    - sed -i "s/HEALTH_CHECK/${API_HEALTH_CHECK_PATH}/g"  ./deploy.yaml
    - sed -i "s/K8S_API_VERSION/${K8S_API_VERSION}/g"  ./deploy.yaml
    - sed -i "s/SKYWALKING_IMAGE/${SKYWALKING_IMAGE}/g"  ./deploy.yaml
    - cat deploy.yaml
    - kubectl apply -f deploy.yaml --record=true




builds_expires_clean:
  stage: done
  variables:
    EXPIRES_DAYS: 30
  tags:
    - docker-runner
  when: always
  allow_failure: true
  before_script:
    - echo "===============  开始清理过期（${EXPIRES_DAYS}天之前）构建目录任务  ==============="
  script:
    - echo "项目目录： ${CI_PROJECT_DIR}"
    - echo "需要清理目录列表："
    - find ${CI_PROJECT_DIR} -mtime +${EXPIRES_DAYS} -name "*"
    - echo "开始清理..."
    - find ${CI_PROJECT_DIR} -mtime +${EXPIRES_DAYS} -name "*" -exec rm -rf {} \;


image_expires_clean:
  stage: done
  image: docker:20.10.5
  variables:
    KEEP_IMAGE_COUNT: 10
  tags:
    - docker-runner
  when: always
  allow_failure: true
  before_script:
    - echo "===============  开始清理过期（保留最近的${KEEP_IMAGE_COUNT}个镜像）docker镜像任务  ==============="
  script:
    - echo "应用${APP_NAME}的镜像列表："
    - docker images | grep ${REPOSITORY_PREFIX} | grep ${APP_NAME}
    - echo $((COUNT=KEEP_IMAGE_COUNT +1))
    - echo "需要清理的${APP_NAME}镜像列表："
    - docker images | grep ${REPOSITORY_PREFIX}  | grep ${APP_NAME} | tail -n +${COUNT}
    - echo "开始清理..."
    - docker images | grep ${REPOSITORY_PREFIX}  | grep ${APP_NAME} | tail -n +${COUNT} | awk '{print $1":"$2}' | xargs -r -t docker rmi